package papi

import (
	"runtime"
)

type MemStat struct {
	ngr float64
	hs  float64
	hi  float64
	ha  float64
}

func (s *MemStat) RecordMemUsage() {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	ngr := float64(runtime.NumGoroutine())
	hs := bToMb(m.HeapSys)  // HeapSys measures the amount of virtual address space reserved for the heap.
	hi := bToMb(m.HeapIdle) // HeapIdle minus HeapReleased estimates the amount of memory that could be returned to the OS, but is being retained by the runtime
	ha := bToMb(m.Alloc)    // HeapAlloc increases as heap objects are allocated and decreases as the heap is swept and unreachable objects are freed

	if s.ngr < ngr {
		s.ngr = ngr
	}
	if s.hs < hs {
		s.hs = hs
	}
	if s.hi < hi {
		s.hi = hi
	}
	if s.ha < ha {
		s.ha = ha
	}
}

func (s *MemStat) NumGoroutines() float64 {
	return s.ngr
}

func (s *MemStat) HeapSys() float64 {
	return s.hs
}

func (s *MemStat) HeapIdle() float64 {
	return s.hi
}

func (s *MemStat) HeapAlloc() float64 {
	return s.ha
}

func bToMb(b uint64) float64 {
	return float64(b / 1024 / 1024)
}
