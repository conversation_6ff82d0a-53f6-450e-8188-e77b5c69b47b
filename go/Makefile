ENV ?= sandbox
AWS_REGION ?= ap-southeast-2
AWS_ACCOUNT_ID ?= ************
INDEX_REBUILD_REPOSITORY ?= audio-music-index-rebuild
INDEX_REBUILD_ECR ?= $(AWS_ACCOUNT_ID).dkr.ecr.$(AWS_REGION).amazonaws.com/$(INDEX_REBUILD_REPOSITORY):latest
SCHEDULE_FILE_NAME ?= triplej

DATABASE_HOST_PRIMARY ?= papi-db
DATABASE_PORT ?= 3306
DATABASE_USER ?= papi
DATABASE_PASS ?= password
DATABASE_NAME ?= papi_dev
PAPI_INGEST_SCHEDULER_QUEUE_URL ?= http://awslocalstack:4566/************/abc-dn-papi-ingestor-schedule
ENVIRONMENT ?= local

build-processor:
	docker exec -it papi-go-bastion sh -c "go build -o /app/bin/processor cmd/lambdas/processor/*.go && \
	mkdir -p /app/src/cmd/lambdas && \
	zip -j /app/src/cmd/lambdas/processor.zip /app/bin/processor && \
	rm -f /app/bin/processor"

deploy-processor:
	awslocal lambda delete-event-source-mapping --uuid $(shell awslocal lambda list-event-source-mappings --function-name papi-processor --query 'EventSourceMappings[?EventSourceArn==`arn:aws:sqs:ap-southeast-2:************:papi-ingest-queue`].[UUID][0][0]') || true
	awslocal lambda delete-function --function-name papi-processor || true
	awslocal lambda create-function \
		--function-name papi-processor \
		--runtime go1.x \
		--zip-file fileb://cmd/lambdas/processor.zip \
		--handler processor \
		--role arn:aws:iam::$(AWS_ACCOUNT_ID):role/lambda-role \
		--tags '{"product":"papi", "role":"processor"}' \
		--environment Variables="{DATABASE_HOST_PRIMARY=$(DATABASE_HOST_PRIMARY),DATABASE_PORT=$(DATABASE_PORT),DATABASE_USER=$(DATABASE_USER),DATABASE_PASS=$(DATABASE_PASS),DATABASE_NAME=$(DATABASE_NAME),PAPI_INGEST_SCHEDULER_QUEUE_URL=$(PAPI_INGEST_SCHEDULER_QUEUE_URL),ENVIRONMENT=$(ENVIRONMENT)}"
	awslocal lambda create-event-source-mapping \
		--function-name papi-processor \
		--event-source-arn arn:aws:sqs:ap-southeast-2:************:papi-ingest-queue \
		--enabled \
		--batch-size 1 \
		--starting-position LATEST

rebuild:
	docker exec -it papi-go-bastion sh -c "go run cmd/index-rebuild/*.go --rebuild-mapping $(filter-out $@,$(MAKECMDGOALS))"

upload-schedule:
	mkdir -p tmp
	curl http://www.abc.net.au/xmlpublic/radio/rapid-schedule-v3/$(SCHEDULE_FILE_NAME).xml -o tmp/$(SCHEDULE_FILE_NAME).xml
	awslocal s3 cp tmp/$(SCHEDULE_FILE_NAME).xml s3://abc-dn-audio-playlist-local/papi/

purge-queue:
	awslocal sqs purge-queue --queue-url $(SQS_QUEUE_URL)
