package papi

type Set[T comparable] struct {
	data map[T]struct{}
}

func NewSet[T comparable]() *Set[T] {
	return &Set[T]{data: make(map[T]struct{})}
}

func (s Set[T]) Add(v T) {
	s.data[v] = struct{}{}
}

func (s Set[T]) Contains(v T) bool {
	_, ok := s.data[v]
	return ok
}

func (s Set[T]) Remove(v T) {
	delete(s.data, v)
}

func PtrValue[T any](v *T, defaultValue T) T {
	if v == nil {
		return defaultValue
	}
	return *v
}
