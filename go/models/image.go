package models

var ImageConfig = AridConfig{
	Path:   "radio.program.image",
	ID:     12,
	Prefix: "pa",
}

type ImageFocalPointHorizontal struct {
	ID   int    `gorm:"primaryKey"`
	Name string `gorm:"not null"`
}

func (ImageFocalPointHorizontal) TableName() string {
	return "image_focal_point_horizontal"
}

type ImageFocalPointVertical struct {
	ID   int    `gorm:"primaryKey"`
	Name string `gorm:"not null"`
}

func (ImageFocalPointVertical) TableName() string {
	return "image_focal_point_vertical"
}

type ImageRole struct {
	ID          int    `gorm:"primaryKey"`
	Role        string `gorm:"not null"`
	Description string `gorm:"not null"`
}

func (ImageRole) TableName() string {
	return "image_role"
}

type ImageSize struct {
	ID          int    `gorm:"primaryKey"`
	ARID        string `gorm:"unique;column:arid"`
	Width       int    `gorm:"not null"`
	Height      int    `gorm:"not null"`
	AspectRatio string `gorm:"not null"`
}

func (ImageSize) TableName() string {
	return "image_size"
}

// Image represents the image table.
type Image struct {
	ID                          int        `gorm:"primaryKey"`
	RoleID                      int        `gorm:"column:role"`
	Role                        *ImageRole `gorm:"foreignKey:RoleID"`
	ARID                        string     `gorm:"unique;column:arid"`
	Title                       string
	MiniSynopsis                string
	ShortSynopsis               string
	MediumSynopsis              string
	CreatedUTC                  DBTime
	LastUpdatedUTC              DBTime `gorm:"autoUpdateTime"`
	URL                         string
	Width                       int
	Height                      int
	AspectRatio                 string
	Credit                      string
	SizeBytes                   int64
	ImageFocalPointHorizontalID int
	ImageFocalPointHorizontal   ImageFocalPointHorizontal `gorm:"foreignKey:ImageFocalPointHorizontalID"`
	ImageFocalPointVerticalID   int
	ImageFocalPointVertical     ImageFocalPointVertical `gorm:"foreignKey:ImageFocalPointVerticalID"`
	Source                      string
	Resized                     []*ImageResized      `gorm:"foreignKey:ImageID"`
	ExternalAttributes          []*ExternalAttribute `gorm:"many2many:image_has_external_attribute;"`
}

// TableName specifies the table name for the Image struct.
func (Image) TableName() string {
	return "image"
}

// ImageResized represents the image_resized table.
type ImageResized struct {
	ID          int       `gorm:"primaryKey"`
	ImageSizeID int       `gorm:"column:image_size_id"`
	Size        ImageSize `gorm:"foreignKey:ImageSizeID"`
	ImageID     int
	Image       Image  `gorm:"foreignKey:ImageID"`
	ARID        string `gorm:"unique;column:arid"`
	URL         string
}

// TableName specifies the table name for the ImageResized struct.
func (ImageResized) TableName() string {
	return "image_resized"
}
