package models

const (
	CategorisationTypeUnion    = "Union"
	CategorisationTypeFallback = "Fallback"
	CategorisationTypeOverride = "Override"
)

var ProgramConfig = AridConfig{
	Path:   "radio.program.program",
	ID:     1,
	Prefix: "pp",
}

var SeriesConfig = AridConfig{
	Path:   "radio.program.series",
	ID:     2,
	Prefix: "ps",
}

// ProgramCategorisationType represents the program_categorisation_type table.
type ProgramCategorisationType struct {
	ID                 int    `gorm:"primaryKey"`
	CategorisationType string `gorm:"uniqueIndex:categorisation_type_unq_idx"`
}

// TableName specifies the table name for the ProgramCategorisationType struct.
func (ProgramCategorisationType) TableName() string {
	return "program_categorisation_type"
}

type Program struct {
	ID                    int  `gorm:"primaryKey"`
	PartOfBrand           *int `gorm:"column:partof_brand"`
	PrimaryImage          *int
	PrimaryWebpage        *int
	ARID                  string `gorm:"unique;column:arid"`
	Title                 string
	MiniSynopsis          *string
	ShortSynopsis         *string
	MediumSynopsis        *string
	CreatedUTC            DBTime
	LastUpdatedUTC        DBTime
	ServiceAirportCode    string
	ABCRadioAirportCode   string `gorm:"unique;column:abcradio_airport_code"`
	PrimaryContactDetails string
	PrimaryEmailAddress   string
	PrimarySMS            string
	PrimaryTelephone      string
	PrimaryMobile         string
	CategorisationTypeID  *int                       `gorm:"column:categorisation_type"`
	CategorisationType    *ProgramCategorisationType `gorm:"foreignKey:CategorisationTypeID"`
	Hash                  string
	Rapid                 string               `gorm:"unique;size:255;default:null"`
	ExternalAttributes    []*ExternalAttribute `gorm:"many2many:program_has_external_attribute;"`
	Images                []*Image             `gorm:"many2many:program_has_image;"`
	OwningServices        []*Service           `gorm:"many2many:program_has_owning_service;"`
	Properties            []*Property          `gorm:"many2many:program_has_properties;"`
	SocialMedia           []*SocialMedia       `gorm:"many2many:program_has_social_media;"`
	Subjects              []*Subject           `gorm:"many2many:program_has_subject;"`
	Webpages              []*Webpage           `gorm:"many2many:program_has_webpage;"`
}

func (Program) TableName() string {
	return "program"
}

type Series struct {
	ID                 int      `gorm:"primaryKey"`
	PrimaryImageID     int      `gorm:"column:primary_image"`
	PrimaryImage       *Image   `gorm:"foreignKey:PrimaryImage"`
	PartOfProgram      *int     `gorm:"column:partof_program"`
	Program            *Program `gorm:"foreignKey:PartOfProgram"`
	PrimaryWebpageID   int      `gorm:"column:primary_webpage"`
	PrimaryWebpage     *Webpage `gorm:"foreignKey:PrimaryWebpageID"`
	ARID               string   `gorm:"unique;column:arid"`
	Title              string
	MiniSynopsis       string
	ShortSynopsis      string
	MediumSynopsis     string
	CreatedUTC         DBTime
	LastUpdatedUTC     DBTime
	Hash               string
	Rapid              string               `gorm:"unique;size:255;default:null"`
	ExternalAttributes []*ExternalAttribute `gorm:"many2many:series_has_external_attribute;"`
	Images             []*Image             `gorm:"many2many:series_has_image;"`
	RegularPresenters  []*Person            `gorm:"many2many:series_has_regular_presenter;"`
	SocialMedia        []*SocialMedia       `gorm:"many2many:series_has_social_media;"`
	Webpages           []*Webpage           `gorm:"many2many:series_has_webpage;"`
	Properties         []*Property          `gorm:"many2many:series_property;"`
}

func (Series) TableName() string {
	return "series"
}
