package models

var ServiceConfig = AridConfig{
	Path:   "radio.program.service",
	ID:     9,
	Prefix: "pi",
}

type Service struct {
	ID                         int `gorm:"primaryKey"`
	PrimaryWebpage             int
	PrimaryImage               int
	ARID                       string `gorm:"unique;column:arid"`
	ServiceID                  string
	Title                      string
	MiniSynopsis               string
	ShortSynopsis              string
	MediumSynopsis             string
	CreatedUTC                 DBTime `gorm:"default:current_timestamp"`
	LastUpdatedUTC             DBTime `gorm:"default:current_timestamp"`
	PrimaryContactDetails      string
	PrimaryEmailAddress        string
	PrimarySMS                 string
	PrimaryTelephone           string
	PrimaryMobile              string
	PrimaryColour              string
	SecondaryColour            string
	HasTrackData               bool
	ScheduleType               int
	ScheduleURL                string
	OnDemandEpisodeStreamURL   string `gorm:"column:ondemand_episode_stream_url"`
	OnDemandEpisodeDownloadURL string `gorm:"column:ondemand_episode_download_url"`
	OnDemandSegmentStreamURL   string `gorm:"column:ondemand_segment_stream_url"`
	OnDemandSegmentDownloadURL string `gorm:"column:ondemand_segment_download_url"`
	ServiceTimezone            string
	ServiceSortOrder           int
	ServicePromote             bool
	ExcludeFromDisplay         bool
	ServiceLocation            string
	Hash                       string
	Rapid                      string `gorm:"default:null;uniqueIndex"`
	ServiceGroup               int    `gorm:"not null"`
	ParentServiceID            int
	ParentService              *Service   `gorm:"foreignKey:ParentServiceID"`
	ChildServices              []*Service `gorm:"foreignKey:ParentServiceID"`
}

func (Service) TableName() string {
	return "service"
}

type ServiceFeed struct {
	ID          int `gorm:"primaryKey"`
	ForService  int
	FeedType    int
	Description string `gorm:"type:varchar(255)"`
	URL         string `gorm:"type:varchar(512)"`
}

func (ServiceFeed) TableName() string {
	return "service_feed"
}

type ServiceHasImage struct {
	ServiceID int `gorm:"primaryKey"`
	ImageID   int `gorm:"primaryKey"`
}

func (ServiceHasImage) TableName() string {
	return "service_has_image"
}

type ServiceHasProperties struct {
	ServiceID  int `gorm:"primaryKey"`
	PropertyID int `gorm:"primaryKey"`
}

func (ServiceHasProperties) TableName() string {
	return "service_has_properties"
}

type ServiceHasWebpage struct {
	ServiceID int `gorm:"primaryKey"`
	WebpageID int `gorm:"primaryKey"`
}

func (ServiceHasWebpage) TableName() string {
	return "service_has_webpage"
}

type ServiceHasExternalAttribute struct {
	ServiceID           int `gorm:"primaryKey"`
	ExternalAttributeID int `gorm:"primaryKey"`
}

func (ServiceHasExternalAttribute) TableName() string {
	return "service_has_external_attribute"
}

type ServiceHasSocialMedia struct {
	ServiceID     int `gorm:"primaryKey"`
	SocialMediaID int `gorm:"primaryKey"`
}

func (ServiceHasSocialMedia) TableName() string {
	return "service_has_social_media"
}

// ServiceFeedType represents the service_feed_type table.
type ServiceFeedType struct {
	ID    int `gorm:"primaryKey"`
	Title string
}

// TableName specifies the table name for the ServiceFeedType struct.
func (ServiceFeedType) TableName() string {
	return "service_feed_type"
}

// ServiceScheduleType represents the service_schedule_type table.
type ServiceScheduleType struct {
	ID          int `gorm:"primaryKey"`
	Title       string
	Description string
}

// TableName specifies the table name for the ServiceScheduleType struct.
func (ServiceScheduleType) TableName() string {
	return "service_schedule_type"
}
