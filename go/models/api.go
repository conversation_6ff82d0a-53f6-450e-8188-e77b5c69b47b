package models

type ApiGroup struct {
	ID    int    `gorm:"primaryKey"`
	Name  string `gorm:"unique;not null"`
	Roles []byte `gorm:"type:longtext;not null;comment:'(DC2Type:array)'"`
}

func (ApiGroup) TableName() string {
	return "api_group"
}

// APIUserUserGroup represents the api_user_user_group table.
type APIUserUserGroup struct {
	UserID  int `gorm:"primaryKey"`
	GroupID int `gorm:"primaryKey"`
}

// TableName specifies the table name for the APIUserUserGroup struct.
func (APIUserUserGroup) TableName() string {
	return "api_user_user_group"
}

type APIUser struct {
	ID                  int    `gorm:"primaryKey"`
	Username            string `gorm:"not null"`
	UsernameCanonical   string `gorm:"not null"`
	Email               string `gorm:"not null"`
	EmailCanonical      string `gorm:"not null"`
	Enabled             bool   `gorm:"not null"`
	Salt                string
	Password            string `gorm:"not null"`
	LastLogin           *DBTime
	ConfirmationToken   string
	PasswordRequestedAt *DBTime
	Roles               []byte `gorm:"type:longtext;not null;comment:'(DC2Type:array)'"`
	Hash                string
	Apikey              string
	CreatedAt           DBTime `gorm:"not null"`
	UpdatedAt           DBTime `gorm:"not null"`
	DateOfBirth         *DBTime
	Firstname           string
	Lastname            string
	Website             string
	Biography           string
	Gender              string
	Locale              string
	Timezone            string
	Phone               string
	FacebookUid         string
	FacebookName        string
	FacebookData        []byte `gorm:"type:longtext;comment:'(DC2Type:json)'"`
	TwitterUid          string
	TwitterName         string
	TwitterData         []byte `gorm:"type:longtext;comment:'(DC2Type:json)'"`
	GplusUid            string
	GplusName           string
	GplusData           []byte `gorm:"type:longtext;comment:'(DC2Type:json)'"`
	Token               string
	TwoStepCode         string
}

func (APIUser) TableName() string {
	return "api_user"
}
