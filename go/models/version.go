package models

import (
	"fmt"
	"strings"
)

const (
	VersionTypeLive     = "Live"
	VersionTypeRepeat   = "Repeat"
	VersionTypeOnDemand = "OnDemand"
)

var VersionConfig = AridConfig{
	Path:   "radio.program.version",
	ID:     5,
	Prefix: "pv",
}

// VersionType represents the version_type table.
type VersionType struct {
	ID          int `gorm:"primaryKey"`
	Title       string
	Description string
}

// TableName specifies the table name for the VersionType struct.
func (VersionType) TableName() string {
	return "version_type"
}

type Version struct {
	ID              int          `gorm:"primaryKey"`
	EpisodeID       *int         `gorm:"column:of_episode"`
	Episode         *Episode     `gorm:"foreignKey:EpisodeID"`
	SegmentID       *int         `gorm:"column:of_segment"`
	Segment         *Segment     `gorm:"foreignKey:SegmentID"`
	VersionTypeID   int          `gorm:"column:version_type"`
	VersionType     *VersionType `gorm:"foreignKey:VersionTypeID"`
	ARID            string
	Title           string `gorm:"not null"`
	MiniSynopsis    string
	ShortSynopsis   string
	MediumSynopsis  string
	CreatedUTC      DBTime
	LastUpdatedUTC  DBTime
	DurationSeconds int `gorm:"column:durationSeconds"`
	Hash            string
	Rapid           string
}

func (Version) TableName() string {
	return "version"
}

func GetVersionType(versionType string) int {
	switch strings.ToLower(versionType) {
	case "live", "repeat":
		return 1
	case "stream":
		return 5
	case "download":
		return 6
	}
	fmt.Println("Unknown version type", strings.ToLower(versionType))
	return 0
}
