package models

var AudioStreamConfig = AridConfig{
	Path:   "radio.program.audiostream",
	ID:     18,
	Prefix: "pt",
}

type AudioFileType struct {
	ID          int    `gorm:"primaryKey"`
	Title       string `gorm:"type:varchar(10);not null"`
	Description string `gorm:"type:varchar(100);not null"`
}

func (AudioFileType) TableName() string {
	return "audio_file_type"
}

// AudioFile represents the audio_file table.
type AudioFile struct {
	ID                 int           `gorm:"primaryKey"`
	TypeID             int           `gorm:"column:type"`
	Type               AudioFileType `gorm:"foreignKey:TypeID"`
	ARID               string        `gorm:"unique;column:arid"`
	Title              string        `gorm:"not null"`
	MiniSynopsis       string        `gorm:"size:100"`
	ShortSynopsis      string        `gorm:"size:250"`
	MediumSynopsis     string        `gorm:"size:500"`
	CreatedUTC         DBTime
	LastUpdatedUTC     DBTime
	URL                string `gorm:"not null"`
	SizeBytes          int64
	Rapid              string               `gorm:"size:255"`
	Hash               string               `gorm:"size:255"`
	ExternalAttributes []*ExternalAttribute `gorm:"many2many:audio_file_has_external_attribute;"`
}

// TableName specifies the table name for the AudioFile struct.
func (AudioFile) TableName() string {
	return "audio_file"
}

type AudioStreamType struct {
	ID          int    `gorm:"primaryKey"`
	Title       string `gorm:"type:varchar(10);not null"`
	Description string `gorm:"type:varchar(100);not null"`
}

func (AudioStreamType) TableName() string {
	return "audio_stream_type"
}

// AudioStream represents the audio_stream table.
type AudioStream struct {
	ID                 int             `gorm:"primaryKey"`
	TypeID             int             `gorm:"column:type"`
	Type               AudioStreamType `gorm:"foreignKey:TypeID"`
	ARID               string          `gorm:"unique;column:arid"`
	Title              string          `gorm:"not null"`
	MiniSynopsis       string          `gorm:"size:100"`
	ShortSynopsis      string          `gorm:"size:250"`
	MediumSynopsis     string          `gorm:"size:500"`
	CreatedUTC         DBTime
	LastUpdatedUTC     DBTime
	URL                string `gorm:"not null"`
	SizeBytes          int64
	Rapid              string               `gorm:"size:255"`
	Hash               string               `gorm:"size:255"`
	ExternalAttributes []*ExternalAttribute `gorm:"many2many:audio_stream_has_external_attribute;"`
}

// TableName specifies the table name for the AudioStream struct.
func (AudioStream) TableName() string {
	return "audio_stream"
}
