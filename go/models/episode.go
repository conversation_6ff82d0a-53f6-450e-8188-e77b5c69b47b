package models

var EpisodeConfig = AridConfig{
	Path:   "radio.program.episode",
	ID:     3,
	Prefix: "pe",
}

type Episode struct {
	ID                        int `gorm:"primaryKey"`
	DerivedFromEpisode        int
	DerivedFromSegment        int
	PrimaryWebpageID          int               `gorm:"column:primary_webpage"`
	PrimaryWebpage            *Webpage          `gorm:"foreignKey:PrimaryWebpageID"`
	PrimaryImageID            *int              `gorm:"column:primary_image"`
	PrimaryImage              *Image            `gorm:"foreignKey:PrimaryImageID"`
	PrimaryPublicationEventID *int              `gorm:"column:primary_publication_event"`
	PrimaryPublicationEvent   *PublicationEvent `gorm:"foreignKey:PrimaryPublicationEventID"`
	IsEpisode                 bool
	IsSegment                 bool
	PartOfSeriesID            int     `gorm:"column:partof_series"`
	Series                    *Series `gorm:"foreignKey:PartOfSeriesID"`
	ARID                      string  `gorm:"unique;column:arid"`
	Title                     string  `gorm:"not null"`
	MiniSynopsis              *string
	ShortSynopsis             *string
	MediumSynopsis            *string
	CreatedUTC                DBTime
	LastUpdatedUTC            DBTime
	ExcludeFromIndex          bool `gorm:"not null"`
	Hash                      string
	Rapid                     string
	Segment                   *Segment             `gorm:"foreignKey:EpisodeID"`
	Images                    []*Image             `gorm:"many2many:episode_has_image;"`
	Presenters                []*Person            `gorm:"many2many:episode_has_presenter;"`
	ExternalAttributes        []*ExternalAttribute `gorm:"many2many:episode_has_external_attribute;"`
	Properties                []*Property          `gorm:"many2many:episode_has_properties;"`
	SocialMedia               []*SocialMedia       `gorm:"many2many:episode_has_social_media;"`
	Subjects                  []*Subject           `gorm:"many2many:episode_has_subject;"`
	Webpages                  []*Webpage           `gorm:"many2many:episode_has_webpage;"`
	ChildSegments             []*Segment           `gorm:"-"`
}

func (Episode) TableName() string {
	return "episode"
}
