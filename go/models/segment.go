package models

type Segment struct {
	ID                        int `gorm:"primaryKey"`
	DerivedFromEpisode        int
	DerivedFromSegment        int
	PrimaryImageID            *int              `gorm:"column:primary_image"`
	PrimaryImage              *Image            `gorm:"foreignKey:PrimaryImageID"`
	PrimaryPublicationEventID *int              `gorm:"column:primary_publication_event"`
	PrimaryPublicationEvent   *PublicationEvent `gorm:"foreignKey:PrimaryPublicationEventID"`
	IsEpisode                 bool
	IsSegment                 bool
	EpisodeID                 int      `gorm:"column:partof_episode"`
	Episode                   *Episode `gorm:"foreignKey:EpisodeID"`
	SegmentID                 int      `gorm:"column:partof_segment"`
	Segment                   *Segment `gorm:"foreignKey:SegmentID"`
	PrimaryWebpageID          int      `gorm:"column:primary_webpage"`
	PrimaryWebpage            *Webpage `gorm:"foreignKey:PrimaryWebpageID"`
	ARID                      string   `gorm:"unique;column:arid"`
	Title                     string   `gorm:"not null"`
	MiniSynopsis              *string
	ShortSynopsis             *string
	MediumSynopsis            *string
	CreatedUTC                DBTime
	LastUpdatedUTC            DBTime
	SegmentOrder              int
	ExcludeFromIndex          bool                 `gorm:"not null"`
	ExternalAttributes        []*ExternalAttribute `gorm:"many2many:segment_has_external_attribute;"`
	Images                    []*Image             `gorm:"many2many:segment_has_image;"`
	Presenters                []*Person            `gorm:"many2many:segment_has_presenter;"`
	Properties                []*Property          `gorm:"many2many:segment_has_properties;"`
	SocialMedia               []*SocialMedia       `gorm:"many2many:segment_has_social_media;"`
	Subjects                  []*Subject           `gorm:"many2many:segment_has_subject;"`
	Webpages                  []*Webpage           `gorm:"many2many:segment_has_webpage;"`
	ProgramID                 *int                 `gorm:"-"`
}

func (Segment) TableName() string {
	return "segment"
}
