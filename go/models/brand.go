package models

type Subject struct {
	ID           int `gorm:"primaryKey"`
	InCategory   int
	Category     *Category `gorm:"foreignKey:InCategory"`
	SubjectLabel string
	Arid         string
}

func (Subject) TableName() string {
	return "subject"
}

// Category represents the category table.
type Category struct {
	ID               int       `gorm:"primaryKey"`
	ParentCategoryID int       `gorm:"column:parent_category"`
	Parent           *Category `gorm:"foreignKey:ParentCategoryID"`
	CategoryLabel    string    `gorm:"not null"`
	ARID             string    `gorm:"unique;column:arid"`
	ValidAudioCount  int
	SortOrder        int
}

// TableName specifies the table name for the Category struct.
func (Category) TableName() string {
	return "category"
}

type Brand struct {
	ID                 int      `gorm:"primaryKey"`
	PrimaryImageID     *int     `gorm:"column:primary_image"`
	PrimaryImage       *Image   `gorm:"foreignKey:PrimaryImageID"`
	PrimaryWebpageID   int      `gorm:"column:primary_webpage"`
	PrimaryWebpage     *Webpage `gorm:"foreignKey:PrimaryWebpageID"`
	ARID               string   `gorm:"unique;column:arid"`
	Title              string
	MiniSynopsis       string
	ShortSynopsis      string
	MediumSynopsis     string
	CreatedUTC         DBTime
	LastUpdatedUTC     DBTime
	BrandID            string
	Images             []*Image             `gorm:"many2many:brand_has_image;"`
	SocialMedia        []*SocialMedia       `gorm:"many2many:brand_has_social_media;"`
	Webpages           []*Webpage           `gorm:"many2many:brand_has_webpage;"`
	ExternalAttributes []*ExternalAttribute `gorm:"many2many:brand_has_external_attribute;"`
}

func (Brand) TableName() string {
	return "brand"
}
