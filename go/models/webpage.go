package models

var WebpageConfig = AridConfig{
	Path:   "radio.program.webpage",
	ID:     15,
	Prefix: "pw",
}

// Webpage represents the webpage table.
type Webpage struct {
	ID                 int    `gorm:"primaryKey"`
	ARID               string `gorm:"unique;column:arid"`
	Title              string `gorm:"not null"`
	MiniSynopsis       string
	ShortSynopsis      string
	MediumSynopsis     string
	CreatedUTC         DBTime               `gorm:"default:current_timestamp"`
	LastUpdatedUTC     DBTime               `gorm:"default:current_timestamp on update current_timestamp"`
	URL                string               `gorm:"not null"`
	ExternalAttributes []*ExternalAttribute `gorm:"many2many:webpage_has_external_attribute;"`
}

// TableName specifies the table name for the Webpage struct.
func (Webpage) TableName() string {
	return "webpage"
}
