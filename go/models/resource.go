package models

const (
	ExternalAttributeTypeOndemandEntityGUID = "XMLOnDemandEntityGuid"
)

// ExternalAttributeType represents the external_attribute_type table.
type ExternalAttributeType struct {
	ID               int `gorm:"primaryKey"`
	ExternalSystemID int
	Title            string `gorm:"not null"`
	Description      string `gorm:"not null"`
}

// TableName specifies the table name for the ExternalAttributeType struct.
func (ExternalAttributeType) TableName() string {
	return "external_attribute_type"
}

type ExternalAttribute struct {
	ID                      int                    `gorm:"primaryKey"`
	ExternalAttributeTypeID *int                   `gorm:"column:external_attribute_type"`
	ExternalAttributeType   *ExternalAttributeType `gorm:"foreignKey:ExternalAttributeTypeID"`
	CreatedUTC              DBTime
	LastUpdatedUTC          DBTime
	Value                   string
}

func (ExternalAttribute) TableName() string {
	return "external_attribute"
}

// PropertyType represents the property_type table.
type PropertyType struct {
	ID          int `gorm:"primaryKey"`
	Title       string
	Description string
}

// TableName specifies the table name for the PropertyType struct.
func (PropertyType) TableName() string {
	return "property_type"
}

type Property struct {
	ID             int `gorm:"primaryKey"`
	PropertyType   *int
	Type           *PropertyType `gorm:"foreignKey:PropertyType"`
	APIUser        int
	CreatedUTC     DBTime `gorm:"default:current_timestamp"`
	LastUpdatedUTC DBTime `gorm:"default:current_timestamp"`
	Value          int8
	Custom         *string
	StartUTC       *DBTime
	EndUTC         *DBTime
}

func (Property) TableName() string {
	return "property"
}

// SocialMediaType represents the social_media_type table.
type SocialMediaType struct {
	ID          int `gorm:"primaryKey"`
	Title       string
	Description string
}

// TableName specifies the table name for the SocialMediaType struct.
func (SocialMediaType) TableName() string {
	return "social_media_type"
}

type SocialMedia struct {
	ID             int             `gorm:"primaryKey"`
	TypeID         int             `gorm:"column:type"`
	Type           SocialMediaType `gorm:"foreignKey:TypeID"`
	ARID           string          `gorm:"unique;column:arid"`
	Title          string
	MiniSynopsis   string
	ShortSynopsis  string
	MediumSynopsis string
	CreatedUTC     DBTime
	LastUpdatedUTC DBTime
	Handle         string
	URL            string
	WidgetID       string
}

func (SocialMedia) TableName() string {
	return "social_media"
}
