package models

var PersonConfig = AridConfig{
	Path:   "radio.program.person",
	ID:     8,
	Prefix: "pr",
}

type Person struct {
	ID                 int      `gorm:"primaryKey"`
	PrimaryImageID     *int     `gorm:"column:primary_image"`
	PrimaryImage       *Image   `gorm:"foreignKey:PrimaryImageID"`
	PrimaryWebpageID   int      `gorm:"column:primary_webpage"`
	PrimaryWebpage     *Webpage `gorm:"foreignKey:PrimaryWebpageID"`
	ARID               string   `gorm:"unique;column:arid"`
	DisplayName        string
	GivenName          string
	FamilyName         string
	MiniSynopsis       string
	ShortSynopsis      string
	MediumSynopsis     string
	CreatedUTC         DBTime `gorm:"default:current_timestamp"`
	LastUpdatedUTC     DBTime `gorm:"default:current_timestamp"`
	Hash               string
	Rapid              string               `gorm:"default:null;uniqueIndex"`
	Images             []*Image             `gorm:"many2many:person_has_image;"`
	Webpages           []*Webpage           `gorm:"many2many:person_has_webpage;"`
	ExternalAttributes []*ExternalAttribute `gorm:"many2many:person_has_external_attribute;"`
	SocialMedia        []*SocialMedia       `gorm:"many2many:person_has_social_media;"`
}

func (Person) TableName() string {
	return "person"
}
