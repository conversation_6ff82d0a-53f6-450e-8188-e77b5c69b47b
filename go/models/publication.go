package models

const (
	OutletClassLive = "LIVE"

	OutletTypeAnalogueRadio    = 1
	OutletTypeMobileLiveStream = 2
	OutletTypeDigitalRadio     = 3
	OutletTypeLiveStream       = 4
)

var PublicationEventConfig = AridConfig{
	Path:   "radio.program.publicationevent",
	ID:     7,
	Prefix: "pu",
}

var OutletConfig = AridConfig{
	Path:   "radio.program.outlet",
	ID:     10,
	Prefix: "po",
}

// OutletType represents the outlet_type table.
type OutletType struct {
	ID          int `gorm:"primaryKey"`
	Title       string
	Class       string
	Description string
}

// TableName specifies the table name for the OutletType struct.
func (OutletType) TableName() string {
	return "outlet_type"
}

type Outlet struct {
	ID             int        `gorm:"primaryKey"`
	OutletTypeID   int        `gorm:"column:outlet_type"`
	OutletType     OutletType `gorm:"foreignKey:OutletTypeID"`
	PartOfService  *int       `gorm:"column:partof_service"`
	Service        *Service   `gorm:"foreignKey:PartOfService"`
	ARID           string     `gorm:"unique;column:arid"`
	Title          string
	MiniSynopsis   string
	ShortSynopsis  string
	MediumSynopsis string
	CreatedUTC     DBTime
	LastUpdatedUTC DBTime
	AudioStreams   []*AudioStream `gorm:"many2many:outlet_has_audio_stream;"`
}

func (Outlet) TableName() string {
	return "outlet"
}

// PublicationArea represents the publication_area table.
type PublicationArea struct {
	ID             int    `gorm:"primaryKey"`
	ARID           string `gorm:"unique;column:arid"`
	Title          string
	Timezone       string
	MiniSynopsis   string
	ShortSynopsis  string
	MediumSynopsis string
}

// TableName specifies the table name for the PublicationArea struct.
func (PublicationArea) TableName() string {
	return "publication_area"
}

// PublicationEventScheduleType represents the publication_event_schedule_type table.
type PublicationEventScheduleType struct {
	ID          int `gorm:"primaryKey"`
	Title       string
	Description string
}

// TableName specifies the table name for the PublicationEventScheduleType struct.
func (PublicationEventScheduleType) TableName() string {
	return "publication_event_schedule_type"
}

// PublicationEventType represents the publication_event_type table.
type PublicationEventType struct {
	ID          int `gorm:"primaryKey"`
	Title       string
	Description string
}

// TableName specifies the table name for the PublicationEventType struct.
func (PublicationEventType) TableName() string {
	return "publication_event_type"
}

type PublicationEvent struct {
	ID                     int `gorm:"primaryKey"`
	HasEpisode             int
	HasSegment             int
	PublicationEventTypeID *int                          `gorm:"column:publication_event_type"`
	PublicationEventType   *PublicationEventType         `gorm:"foreignKey:PublicationEventTypeID"`
	VersionID              int                           `gorm:"column:has_version"`
	Version                *Version                      `gorm:"foreignKey:VersionID"`
	ARID                   string                        `gorm:"unique;column:arid"`
	ScheduleTypeID         *int                          `gorm:"column:schedule_type"`
	ScheduleType           *PublicationEventScheduleType `gorm:"foreignKey:ScheduleTypeID"`
	CreatedUTC             DBTime
	LastUpdatedUTC         DBTime
	StartUTC               DBTime
	EndUTC                 DBTime
	DefiningTimezone       *string
	Hash                   string
	Rapid                  string
	DisableSync            bool
	Areas                  []*PublicationArea         `gorm:"many2many:publication_event_has_area;"`
	AudioFiles             []*AudioFile               `gorm:"many2many:publication_event_has_audio_file;"`
	AudioStreams           []*AudioStream             `gorm:"many2many:publication_event_has_audio_stream;"`
	ExternalAttributes     []*ExternalAttribute       `gorm:"many2many:publication_event_has_external_attribute;"`
	Outlets                []*Outlet                  `gorm:"many2many:publication_event_has_outlet;"`
	OutletLink             *PublicationEventHasOutlet `gorm:"foreignKey:PublicationEventID"`
}

func (PublicationEvent) TableName() string {
	return "publication_event"
}

func (p *PublicationEvent) Clone() *PublicationEvent {
	clone := &PublicationEvent{
		ID:                     p.ID,
		HasEpisode:             p.HasEpisode,
		HasSegment:             p.HasSegment,
		PublicationEventTypeID: p.PublicationEventTypeID,
		PublicationEventType:   p.PublicationEventType,
		VersionID:              p.VersionID,
		Version:                p.Version,
		ARID:                   p.ARID,
		ScheduleTypeID:         p.ScheduleTypeID,
		ScheduleType:           p.ScheduleType,
		CreatedUTC:             p.CreatedUTC,
		LastUpdatedUTC:         p.LastUpdatedUTC,
		StartUTC:               p.StartUTC,
		EndUTC:                 p.EndUTC,
		DefiningTimezone:       p.DefiningTimezone,
		Hash:                   p.Hash,
		Rapid:                  p.Rapid,
		DisableSync:            p.DisableSync,
		Areas:                  p.Areas,
		AudioFiles:             p.AudioFiles,
		AudioStreams:           p.AudioStreams,
		ExternalAttributes:     p.ExternalAttributes,
		Outlets:                p.Outlets,
		OutletLink:             p.OutletLink,
	}
	return clone
}

type PublicationEventHasOutlet struct {
	PublicationEventID int `gorm:"primaryKey;column:publication_event"`
	OutletID           int `gorm:"primaryKey;column:outlet"`
}

func (PublicationEventHasOutlet) TableName() string {
	return "publication_event_has_outlet"
}
