package models

import (
	"database/sql/driver"
	"errors"
	"fmt"
	"time"
)

type AridConfig struct {
	ID          int    `gorm:"primaryKey"`
	EntityName  string `gorm:"not null"`
	Description string `gorm:"not null"`
	Table       string `gorm:"not null"`
	Path        string `gorm:"not null"`
	Prefix      string `gorm:"not null"`
}

func (AridConfig) TableName() string {
	return "arid_config"
}

type ExternalSystem struct {
	ID          int    `gorm:"primaryKey"`
	Title       string `gorm:"not null"`
	Description string `gorm:"not null"`
}

func (ExternalSystem) TableName() string {
	return "external_system"
}

type MigrationVersion struct {
	Version       string `gorm:"primaryKey"`
	ExecutedAt    DBTime `gorm:"type:datetime"`
	ExecutionTime int    `gorm:"type:int"`
}

func (MigrationVersion) TableName() string {
	return "migration_versions"
}

type DBTime time.Time

func (j *DBTime) Scan(value interface{}) error {
	t, ok := value.(time.Time)
	if !ok {
		return errors.New(fmt.Sprint("Failed to unmarshal DBTime value:", value))
	}
	_, offset := t.Zone()
	*j = DBTime(t.UTC().Add(time.Duration(offset) * time.Second))
	return nil
}

// Value return json value, implement driver.Valuer interface
func (j DBTime) Value() (driver.Value, error) {
	return time.Time(j).UTC(), nil
}

func (j DBTime) Format(layout string) string {
	return time.Time(j).Format(layout)
}

func (j DBTime) Add(duration time.Duration) DBTime {
	return DBTime(time.Time(j).Add(duration))
}
