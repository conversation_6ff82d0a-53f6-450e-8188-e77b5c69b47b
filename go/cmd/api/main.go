package main

import (
	"errors"
	"log/slog"
	"net/http"
	"os"

	base "stash.abc-dev.net.au/rmp/rmp-program-api"
	"stash.abc-dev.net.au/rmp/rmp-program-api/app/api"
	"stash.abc-dev.net.au/rmp/rmp-program-api/indexing"
)

type Episode struct {
	Id           int64
	Arid         string
	Title        string
	MiniSynopsis string
}

func main() {

	config := base.LoadConfig()

	db, err := base.ConnectToDb(config.Database)
	if err != nil {
		slog.Error("Error connecting to db", "error", err)
		os.Exit(1)
	}

	srch, err := indexing.ConnectToSearch(config.Opensearch)
	if err != nil {
		panic(err)
	}
	err = indexing.CreateIndexIfNotExists(srch, base.StringEnvOrDefault("MAPPING_PATH", "/app/mappings/opensearch"), indexing.IndexProgramItem, indexing.IndexLive)
	if err != nil {
		panic(err)
	}
	h := api.NewHandler(db, srch)
	e := api.NewRestRouter(h)

	// Start server
	if err := e.Start(":8080"); err != nil && !errors.Is(err, http.ErrServerClosed) {
		slog.Error("failed to start server", "error", err)
		os.Exit(1)
	}
}
