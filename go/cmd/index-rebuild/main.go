package main

import (
	"fmt"
	"log/slog"
	"os"

	cli "github.com/urfave/cli/v2"

	base "stash.abc-dev.net.au/rmp/rmp-program-api"
	index_rebuild "stash.abc-dev.net.au/rmp/rmp-program-api/app/index-rebuild"
)

func main() {
	slog.SetDefault(slog.New(slog.NewJSONHandler(os.Stdout, nil)))
	slog.Info("Rebuild started")
	config := base.LoadConfig()
	dbFetcher := index_rebuild.NewDBFetcher(base.ParseDBConfig(config.Database))
	fc := index_rebuild.NewFlowController(dbFetcher, config.Opensearch)
	err := fc.Precache()
	if err != nil {
		panic(err)
	}
	app := &cli.App{
		Name:  "index-rebuild",
		Usage: "Rebuild program index",
		Flags: []cli.Flag{
			&cli.BoolFlag{
				Name:  "rebuild-mapping",
				Usage: "Whether or not to rebuild mapping",
			},
		},
		Commands: []*cli.Command{
			{
				Name:      "arid",
				Usage:     "Rebuild single ARID",
				Args:      true,
				ArgsUsage: "ARID",
				Action: func(cCtx *cli.Context) error {
					if cCtx.Args().Len() != 1 {
						return fmt.Errorf("expecting one ARID")
					}
					fc.RebuildMapping(cCtx.Bool("rebuild-mapping"))
					db, err := dbFetcher.FilterEpisodeByARID(cCtx.Args().First())
					if err != nil {
						return err
					}
					return fc.Exec(db)
				},
			},
			{
				Name:      "program",
				Usage:     "Rebuild specified program",
				Args:      true,
				ArgsUsage: "Program ARID",
				Action: func(cCtx *cli.Context) error {
					if cCtx.Args().Len() != 1 {
						return fmt.Errorf("expecting one Program ARID")
					}
					fc.RebuildMapping(cCtx.Bool("rebuild-mapping"))
					db, err := dbFetcher.FilterEpisodeByProgram(cCtx.Args().First())
					if err != nil {
						return err
					}
					return fc.Exec(db)
				},
			},
			{
				Name:  "range",
				Usage: "Rebuild with specified range",
				Flags: []cli.Flag{
					&cli.StringFlag{
						Name:  "from",
						Usage: "Start time, format: 2006-01-02T15:04:05 in UTC timezone",
					},
					&cli.StringFlag{
						Name:  "to",
						Usage: "End time, format: 2006-01-02T15:04:05 in UTC timezone",
					},
				},
				Action: func(cCtx *cli.Context) error {
					fc.RebuildMapping(cCtx.Bool("rebuild-mapping"))
					db, err := dbFetcher.FilterEpisodeByRange(cCtx.String("from"), cCtx.String("to"))
					if err != nil {
						return err
					}
					return fc.Exec(db)
				},
			},
		},
	}
	if err := app.Run(os.Args); err != nil {
		panic(err)
	}
}
