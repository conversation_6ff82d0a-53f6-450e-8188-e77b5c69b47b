package main

import (
	"context"
	"encoding/xml"
	"fmt"
	"io"
	"log/slog"
	"os"
	"strings"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	jsoniter "github.com/json-iterator/go"

	base "stash.abc-dev.net.au/rmp/rmp-program-api"
	"stash.abc-dev.net.au/rmp/rmp-program-api/r3"
)

var json = jsoniter.ConfigCompatibleWithStandardLibrary

var (
	s3Client *s3.Client
)

func init() {
	opts := &slog.HandlerOptions{
		Level: slog.LevelInfo,
	}

	if os.Getenv("ENVIRONMENT") == "release" || os.Getenv("ENVIRONMENT") == "local" {
		opts.Level = slog.LevelDebug
	}

	slog.SetDefault(slog.New(slog.NewJSONHandler(os.Stdout, opts)))
	// Initialize the S3 client outside of the handler, during the init phase
	cfg, err := config.LoadDefaultConfig(context.TODO())
	if err != nil {
		slog.Error("unable to load SDK config", "error", err.Error())
		panic(err)
	}

	s3Client = s3.NewFromConfig(cfg)

	queueUrl := os.Getenv("PAPI_INGEST_SCHEDULER_QUEUE_URL")
	if queueUrl == "" {
		panic("PAPI_INGEST_SCHEDULER_QUEUE_URL is not set")
	}
	// set queue url to r3 package
	r3.QueueUrl = queueUrl

	// connect to db
	config := base.DBConfiguration{
		Port:        os.Getenv("DATABASE_PORT"),
		User:        os.Getenv("DATABASE_USER"),
		Password:    os.Getenv("DATABASE_PASS"),
		Name:        os.Getenv("DATABASE_NAME"),
		PrimaryHost: os.Getenv("DATABASE_HOST_PRIMARY"),
	}
	r3.ConnectToDB(config)

	// connect to sqs
	r3.ConnectToSQS(cfg)
}

func handleRequest(ctx context.Context, event events.SQSEvent) error {
	slog.Info("Received SQS event", "event", event)
	for _, record := range event.Records {
		logger := slog.With(
			slog.String("messageID", record.MessageId),
			slog.String("receiptHandle", record.ReceiptHandle),
			slog.String("body", record.Body),
		)

		var snsEvent events.SNSEntity
		err := json.Unmarshal([]byte(record.Body), &snsEvent)
		if err != nil {
			logger.Error("Failed to unmarshal SNS event", "error", err.Error(), "body", record.Body)
			return err
		}

		logger = slog.With(
			slog.String("messageID", record.MessageId),
			slog.String("receiptHandle", record.ReceiptHandle),
			slog.String("message", snsEvent.Message),
		)

		var s3Event events.S3Event
		err = json.Unmarshal([]byte(snsEvent.Message), &s3Event)
		if err != nil {
			logger.Error("Failed to unmarshal S3 event", "error", err.Error(), "body", snsEvent.Message)
			return err
		}

		err = handleS3Event(ctx, s3Event)
		if err != nil {
			logger.Error("Failed to handle S3 event", "error", err.Error())
			return err
		}
	}
	return nil
}

func handleS3Event(ctx context.Context, s3Event events.S3Event) error {
	slog.Info("Handling S3 event", "s3Event", s3Event)
	for _, s3Record := range s3Event.Records {
		logger := slog.With(
			slog.String("bucket", s3Record.S3.Bucket.Name),
			slog.String("key", s3Record.S3.Object.Key),
			slog.String("eTag", s3Record.S3.Object.ETag),
			slog.String("versionID", s3Record.S3.Object.VersionID),
			slog.Time("eventTime", s3Record.EventTime),
		)
		if !strings.HasSuffix(s3Record.S3.Object.Key, ".xml") {
			logger.Info("Skipping non-XML file")
			continue
		}
		resp, err := s3Client.GetObject(ctx, &s3.GetObjectInput{
			Bucket:    aws.String(s3Record.S3.Bucket.Name),
			Key:       aws.String(s3Record.S3.Object.Key),
			VersionId: aws.String(s3Record.S3.Object.VersionID),
		})
		if err != nil {
			logger.Error("Failed to get object", "error", err.Error())
			return err
		}
		defer resp.Body.Close()
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			logger.Error("Failed to read object", "error", err.Error())
			return err
		}
		// slog.Debug("Received object body", "bucket", s3Record.S3.Bucket.Name, "key", s3Record.S3.Object.Key, "body", string(body))
		logger.Debug("Received object body", "body", string(body))

		// Unmarshal the XML into Go structs
		var schedule r3.Schedule
		err = xml.Unmarshal(body, &schedule)
		if err != nil {
			logger.Error("failed to unmarshal XML", "error", err.Error(), "body", string(body))
			return fmt.Errorf("failed to unmarshal XML: %v", err)
		}

		// add service id to logger
		logger = logger.With(
			slog.String("serviceID", schedule.Service.ServiceID),
		)

		start := time.Now()
		logger.Info("Processing Schedule", "datetime", start.Format(time.RFC3339))

		err = r3.ProcessSchedule(schedule)
		if err != nil {
			slog.Error("error processing schedule", "error", err.Error())
			return fmt.Errorf("error processing schedule: %v", err)
		}

		end := time.Now()
		duration := end.Sub(start)
		logger.Info("Schedule processed", "datetime", end.Format(time.RFC3339), "durationMs", duration.Milliseconds())
	}

	return nil
}

func main() {
	lambda.Start(handleRequest)
}
