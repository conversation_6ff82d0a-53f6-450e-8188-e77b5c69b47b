# PAPI-go

## Start Development Environment
1. Run `make up` under `infrastructure/docker` folder.
2. Run all `go get` commands under `go` folder.

## Build and Deployment R3 ingestor
1. Run `make build-processor`
2. Run `make deploy-processor`

## Upload new schedule file into 
1. Update [Makefile](Makefile) and change `SCHEDULE_FILE_NAME` to the desired filename.
2. Run `make upload-schedule`

## Check Lambda logs
1. `awslocalstack` will start a container that name starts with `papi-awslocalstack-lambda-papi-processor-`
2. Use `docker logs ${container_name}` to display Lambda logs. Or you can check the logs via docker desktop.
3. `awslocal logs create-log-group --log-group-name /aws/lambda/papi-processor`