package papi

import (
	"encoding/json"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"os"
	"strconv"
	"time"

	"github.com/spf13/viper"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

type IndexingConfiguration struct {
	URL      string `mapstructure:"OPENSEARCH_URL"`
	User     string `mapstructure:"OPENSEARCH_USER"`
	Password string `mapstructure:"OPENSEARCH_PASSWORD"`
	Region   string `mapstructure:"AMAZON_AWS_REGION"`
}

type AwsConfiguration struct {
	AwsRegion  string `mapstructure:"AMAZON_AWS_REGION"`
	Connection string `mapstructure:"AMAZON_AWS_SECRET_KEY"`
	EndPoint   string `mapstructure:"AWS_ENDPOINT"`
}

type Configuration struct {
	Database   DBConfiguration       `mapstructure:",squash"`
	Opensearch IndexingConfiguration `mapstructure:",squash"`
	Aws        AwsConfiguration      `mapstructure:",squash"`
}

type DBConfiguration struct {
	Port                 string `mapstructure:"DATABASE_PORT"`
	User                 string `mapstructure:"DATABASE_USER"`
	Password             string `mapstructure:"DATABASE_PASS"`
	Name                 string `mapstructure:"DATABASE_NAME"`
	PrimaryHost          string `mapstructure:"DATABASE_HOST_PRIMARY"`
	ReplicaHost          string `mapstructure:"DATABASE_HOST_REPLICA"`
	AllowNativePasswords bool   `mapstructure:"DATABASE_ALLOW_NATIVE_PASSWORDS"`
}

func ConnectToDb(config DBConfiguration) (*gorm.DB, error) {
	return gorm.Open(mysql.Open(ParseDBConfig(config)), &gorm.Config{})
}

func ParseDBConfig(config DBConfiguration) string {
	conn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local", config.User, config.Password, config.PrimaryHost, config.Port, config.Name)
	return conn
}

func LoadConfig() Configuration {
	viper.AddConfigPath(".")
	viper.SetConfigName(".env.local")
	viper.AutomaticEnv()
	viper.SetConfigType("env")

	if err := viper.ReadInConfig(); err != nil {
		fmt.Printf("Error reading config file, %s", err)
	}

	var config Configuration

	err := viper.Unmarshal(&config)
	if err != nil {
		fmt.Printf("Unable to decode into struct, %v", err)
	}
	return config
}

func StringEnvOrDefault(key, defaultValue string) string {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}
	return value
}

func IntEnvOrDefault(key string, defaultValue int) int {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}
	v, err := strconv.Atoi(value)
	if err != nil {
		panic(err)
	}
	return v
}

func DatetimeEnv(key string) time.Time {
	value := os.Getenv(key)
	if value == "" {
		return time.Time{}
	}
	v, err := time.ParseInLocation("2006-01-02T15:04:05", value, time.UTC)
	if err != nil {
		panic(err)
	}
	return v
}

var taskInfo *ECSTask

func TaskInfo() *ECSTask {
	if taskInfo != nil {
		return taskInfo
	}
	if v, exist := os.LookupEnv("ECS_CONTAINER_METADATA_URI_V4"); exist {
		c := http.Client{
			Timeout: 5 * time.Second,
		}
		resp, err := c.Get(v + "/task")
		if err != nil {
			slog.Error("failed to get metadata", "error", err)
			return nil
		}
		defer resp.Body.Close()
		body, err := io.ReadAll(resp.Body)
		if resp.StatusCode != http.StatusOK {
			slog.Error("failed to get metadata", "status", resp.Status, "body", json.RawMessage(body))
			return nil
		}
		if err != nil {
			slog.Error("failed to read metadata", "error", err)
			return nil
		}
		slog.Info("task info", "info", json.RawMessage(body))
		taskInfo = &ECSTask{}
		err = json.Unmarshal(body, taskInfo)
		if err != nil {
			slog.Error("failed to decode metadata", "error", err)
			return nil
		}
		return taskInfo
	} else {
		return nil
	}
}

type ECSTask struct {
	Cluster  string `json:"Cluster"`
	TaskARN  string `json:"TaskARN"`
	Family   string `json:"Family"`
	Revision string `json:"Revision"`
	Limits   struct {
		CPU    int `json:"CPU"`
		Memory int `json:"Memory"`
	} `json:"Limits"`
	LaunchType              string `json:"LaunchType"`
	EphemeralStorageMetrics struct {
		Utilized int `json:"Utilized"`
		Reserved int `json:"Reserved"`
	} `json:"EphemeralStorageMetrics"`
}
