package indexing

import (
	"fmt"
	"log/slog"
	"time"

	"gorm.io/gorm"

	base "stash.abc-dev.net.au/rmp/rmp-program-api"
	"stash.abc-dev.net.au/rmp/rmp-program-api/models"
)

type DBDataConvertor struct {
	db    *gorm.DB
	cache *IndexItemCache
}

func NewDBDataConvertor(db *gorm.DB) *DBDataConvertor {
	return &DBDataConvertor{
		db:    db,
		cache: NewIndexItemCache(),
	}
}

func (m *DBDataConvertor) Precache() error {
	defer slog.Info("Precache completed")
	err := m.CacheAPIUsers()
	if err != nil {
		return err
	}
	err = m.CacheCategories()
	if err != nil {
		return err
	}
	err = m.CacheOutlets()
	if err != nil {
		return err
	}
	return nil
}

func (m *DBDataConvertor) CacheAPIUsers() error {
	users := []*models.APIUser{}
	result := m.db.Model(&models.APIUser{}).Where("roles like '%\"ROLE_API\"%'").Find(&users)
	if result.Error != nil {
		return fmt.Errorf("error when cache API users: %w", result.Error)
	}
	for _, user := range users {
		m.cache.AddAPIUser(user.ID, user)
	}
	return nil
}

func (m *DBDataConvertor) FromDBEpisode(e *models.Episode) (*ProgramItem, error) {
	if e == nil {
		return nil, nil
	}
	var err error
	item := &ProgramItem{
		Entity:            EntityEpisode,
		ARID:              e.ARID,
		Title:             e.Title,
		MediumSynopsis:    e.MediumSynopsis,
		ShortSynopsis:     e.ShortSynopsis,
		MiniSynopsis:      e.MiniSynopsis,
		ExcludeFromIndex:  e.ExcludeFromIndex,
		LastPublishedDate: e.LastUpdatedUTC.Format(DateFormat),
		Live:              make([]*Live, 0),
	}
	if e.Series != nil {
		item.Series = m.FromDBSeries(e.Series)
		m.UpdateLastUpdate(item, time.Time(e.Series.LastUpdatedUTC))
		if e.Series.PartOfProgram != nil {
			item.Program, err = m.FetchProgramByID(*e.Series.PartOfProgram)
			if err != nil {
				return nil, err
			}
			m.UpdateLastUpdate(item, item.Program.LastUpdatedUTC)
			item.CreatingBrand = item.Program.CreatingBrand
			if item.CreatingBrand != nil {
				m.UpdateLastUpdate(item, item.CreatingBrand.LastUpdatedUTC)
			}
			item.CreatingService = item.Program.OwningService
			if item.CreatingService != nil {
				m.UpdateLastUpdate(item, item.CreatingService.LastUpdatedUTC)
			}
		}
	}
	if e.PrimaryWebpage != nil {
		m.UpdateLastUpdate(item, time.Time(e.PrimaryWebpage.LastUpdatedUTC))
		item.PrimaryWebPage = m.FromDBWebpage(e.PrimaryWebpage)
	}
	primaryImage, err := m.FetchImageByID(e.PrimaryImageID)
	if err != nil {
		return nil, err
	}
	if primaryImage != nil {
		m.UpdateLastUpdate(item, primaryImage.LastUpdatedUTC)
	}
	item.PrimaryImage = primaryImage
	images, err := m.FetchLinkedImages(e)
	if err != nil {
		return nil, err
	}
	item.Images = images

	childSetments := make([]ChildSegment, 0, len(e.ChildSegments))
	for _, segment := range e.ChildSegments {
		childSetments = append(childSetments, ChildSegment{
			ARID:  segment.ARID,
			Title: segment.Title,
		})
		m.UpdateLastUpdate(item, time.Time(segment.LastUpdatedUTC))
	}
	item.ChildSegments = childSetments
	item.Presenters, err = m.FetchPresenters(e)
	if err != nil {
		return nil, err
	}
	for _, presenter := range item.Presenters {
		m.UpdateLastUpdate(item, presenter.LastUpdatedUTC)
	}
	lives, primaryEvent, err := m.FetchPublicationEvents(e)
	if err != nil {
		return nil, err
	}
	item.Live = lives
	item.PrimaryPublicationEvent = primaryEvent
	item.Categories, err = m.FetchCategories(e)
	if err != nil {
		return nil, err
	}
	// item.ServiceGUID, err = m.FetchServiceGUID(e)
	// if err != nil {
	// 	return nil, err
	// }
	item.Properties, err = m.FetchProperties(e)
	if err != nil {
		return nil, err
	}
	return item, err
}

func (m *DBDataConvertor) FromDBSegment(s *models.Segment) (*ProgramItem, error) {
	if s == nil {
		return nil, nil
	}
	var err error
	item := &ProgramItem{
		Entity:            EntitySegment,
		ARID:              s.ARID,
		Title:             s.Title,
		MediumSynopsis:    s.MediumSynopsis,
		ShortSynopsis:     s.ShortSynopsis,
		MiniSynopsis:      s.MiniSynopsis,
		ExcludeFromIndex:  s.ExcludeFromIndex,
		LastPublishedDate: s.LastUpdatedUTC.Format(DateFormat),
		Live:              make([]*Live, 0),
		ChildSegments:     make([]ChildSegment, 0),
	}
	if s.ProgramID != nil {
		item.Program, err = m.FetchProgramByID(*s.ProgramID)
		if err != nil {
			return nil, err
		}
		m.UpdateLastUpdate(item, item.Program.LastUpdatedUTC)
		item.CreatingBrand = item.Program.CreatingBrand
		if item.CreatingBrand != nil {
			m.UpdateLastUpdate(item, item.CreatingBrand.LastUpdatedUTC)
		}
		item.CreatingService = item.Program.OwningService
		if item.CreatingService != nil {
			m.UpdateLastUpdate(item, item.CreatingService.LastUpdatedUTC)
		}
	}
	if s.PrimaryWebpage != nil {
		m.UpdateLastUpdate(item, time.Time(s.PrimaryWebpage.LastUpdatedUTC))
		item.PrimaryWebPage = m.FromDBWebpage(s.PrimaryWebpage)
	}
	primaryImage, err := m.FetchImageByID(s.PrimaryImageID)
	if err != nil {
		return nil, err
	}
	item.PrimaryImage = primaryImage
	images, err := m.FetchLinkedImages(s)
	if err != nil {
		return nil, err
	}
	item.Images = images
	episodeImage, err := m.FetchImageByID(s.Episode.PrimaryImageID)
	if err != nil {
		return nil, err
	}
	item.PartOfEpisode = &Episode{
		ARID:           s.Episode.ARID,
		Title:          s.Episode.Title,
		MediumSynopsis: base.PtrValue(s.Episode.MediumSynopsis, ""),
		ShortSynopsis:  base.PtrValue(s.Episode.ShortSynopsis, ""),
		MiniSynopsis:   base.PtrValue(s.Episode.MiniSynopsis, ""),
		PrimaryImage:   episodeImage,
	}
	item.Series = m.FromDBSeries(s.Episode.Series)
	m.UpdateLastUpdate(item, time.Time(s.Episode.LastUpdatedUTC))
	item.Presenters, err = m.FetchPresenters(s)
	if err != nil {
		return nil, err
	}
	for _, presenter := range item.Presenters {
		m.UpdateLastUpdate(item, presenter.LastUpdatedUTC)
	}
	lives, primaryEvent, err := m.FetchPublicationEvents(s)
	if err != nil {
		return nil, err
	}
	item.Live = lives
	if primaryEvent == nil {
		_, primaryEvent, err = m.FetchPublicationEvents(s.Episode)
		if err != nil {
			return nil, err
		}
	}
	item.PrimaryPublicationEvent = primaryEvent
	item.Categories, err = m.FetchCategories(s)
	if err != nil {
		return nil, err
	}
	// item.ServiceGUID, err = m.FetchServiceGUID(s)
	// if err != nil {
	// 	return nil, err
	// }
	item.Properties, err = m.FetchProperties(s)
	if err != nil {
		return nil, err
	}
	return item, nil
}

func (m *DBDataConvertor) FetchServiceGUID(parent any) (string, error) {
	attributes := []*models.ExternalAttribute{}
	switch v := parent.(type) {
	case *models.Episode:
		result := m.db.Model(&models.ExternalAttribute{}).
			Joins("INNER JOIN external_attribute_type t ON external_attribute.external_attribute_type=t.id").
			Joins("INNER JOIN episode_has_external_attribute eea ON eea.external_attribute=external_attribute.id").
			Where("eea.episode = ? AND t.title=?", v.ID, models.ExternalAttributeTypeOndemandEntityGUID).Limit(1).
			Find(&attributes)
		if result.Error != nil {
			return "", fmt.Errorf("error when searching external attributes of episode %s: %w", v.ARID, result.Error)
		}
	case *models.Segment:
		result := m.db.Model(&models.ExternalAttribute{}).
			Joins("INNER JOIN external_attribute_type t ON external_attribute.external_attribute_type=t.id").
			Joins("INNER JOIN segment_has_external_attribute sea ON sea.external_attribute=external_attribute.id").
			Where("sea.segment = ? AND t.title=?", v.ID, models.ExternalAttributeTypeOndemandEntityGUID).Limit(1).
			Find(&attributes)
		if result.Error != nil {
			return "", fmt.Errorf("error when searching external attributes of segment %s: %w", v.ARID, result.Error)
		}
	default:
		return "", fmt.Errorf("unsupported FetchServiceGUID type %T", parent)
	}
	if len(attributes) == 0 {
		return "", nil
	}
	return attributes[0].Value, nil
}

func (m *DBDataConvertor) FromDBProgram(dbProgram *models.Program) (*Program, error) {
	if dbProgram == nil {
		return nil, nil
	}
	categorisationType := models.CategorisationTypeFallback
	if dbProgram.CategorisationType != nil {
		categorisationType = dbProgram.CategorisationType.CategorisationType
	}
	program := &Program{
		ARID:                  dbProgram.ARID,
		Title:                 dbProgram.Title,
		ShortSynopsis:         base.PtrValue(dbProgram.ShortSynopsis, ""),
		MiniSynopsis:          base.PtrValue(dbProgram.MiniSynopsis, ""),
		PrimaryContactDetails: dbProgram.PrimaryContactDetails,
		PrimaryEmailAddress:   dbProgram.PrimaryEmailAddress,
		PrimaryMobile:         dbProgram.PrimaryMobile,
		PrimarySMS:            dbProgram.PrimarySMS,
		PrimaryTelephone:      dbProgram.PrimaryTelephone,
		ABCRadioAirportCode:   dbProgram.ABCRadioAirportCode,
		CategorisationType:    categorisationType,
		LastUpdatedUTC:        time.Time(dbProgram.LastUpdatedUTC),
	}
	primaryImage, err := m.FetchImageByID(dbProgram.PrimaryImage)
	if err != nil {
		return nil, err
	}
	program.PrimaryImage = primaryImage
	images, err := m.FetchLinkedImages(dbProgram)
	if err != nil {
		return nil, err
	}
	program.Images = images
	brand, err := m.FetchBrandByID(dbProgram.PartOfBrand)
	if err != nil {
		return nil, err
	}
	program.CreatingBrand = brand
	service, err := m.FetchOwningService(dbProgram.ID)
	if err != nil {
		return nil, err
	}
	program.OwningService = service
	return program, nil
}

func (m *DBDataConvertor) FromDBSeries(s *models.Series) *Series {
	if s == nil {
		return nil
	}
	return &Series{
		ARID:           s.ARID,
		Title:          s.Title,
		LastUpdatedUTC: time.Time(s.LastUpdatedUTC),
	}
}

func (m *DBDataConvertor) FromDBBrand(b *models.Brand) *Brand {
	if b == nil {
		return nil
	}
	return &Brand{
		ARID:           b.ARID,
		Title:          b.Title,
		BrandID:        b.BrandID,
		LastUpdatedUTC: time.Time(b.LastUpdatedUTC),
	}
}

func (m *DBDataConvertor) FetchPresenters(parent any) ([]*Person, error) {
	var err error
	persons := []*models.Person{}
	var result *gorm.DB
	switch v := parent.(type) {
	case *models.Episode:
		result = m.db.Model(&models.Person{}).Joins("PrimaryWebpage").Joins("INNER JOIN episode_has_presenter e_hp ON e_hp.person=person.id").Where("e_hp.episode = ?", v.ID).Find(&persons)
		if result.Error != nil {
			return nil, fmt.Errorf("error when searching presenters of episode %s: %w", v.ARID, result.Error)
		}
	case *models.Segment:
		result = m.db.Model(&models.Person{}).Joins("PrimaryWebpage").Joins("INNER JOIN segment_has_presenter s_hp ON s_hp.person=person.id").Where("s_hp.segment = ?", v.ID).Find(&persons)
		if result.Error != nil {
			return nil, fmt.Errorf("error when searching presenters of segment %s: %w", v.ARID, result.Error)
		}
		if len(persons) == 0 && v.Episode != nil {
			return m.FetchPresenters(v.Episode)
		}
	default:
		return nil, fmt.Errorf("unsupported FetchPresenters type %T", parent)
	}
	presenters := make([]*Person, 0, len(persons))
	for _, dbPerson := range persons {
		presenter := m.cache.GetPresenter(dbPerson.ID)
		if presenter == nil {
			presenter, err = m.FromDBPerson(dbPerson)
			if err != nil {
				return nil, err
			}
			m.cache.AddPresenter(dbPerson.ID, presenter)
		}
		presenters = append(presenters, presenter)
	}
	return presenters, nil
}

func (m *DBDataConvertor) FromDBPerson(p *models.Person) (*Person, error) {
	if p == nil {
		return nil, nil
	}
	image, err := m.FetchImageByID(p.PrimaryImageID)
	if err != nil {
		return nil, err
	}
	return &Person{
		ARID:           p.ARID,
		DisplayName:    p.DisplayName,
		PrimaryImage:   image,
		LastUpdatedUTC: time.Time(p.LastUpdatedUTC),
	}, nil
}

func (m *DBDataConvertor) FetchProgramByID(id int) (*Program, error) {
	program := m.cache.GetProgram(id)
	if program != nil {
		return program, nil
	}
	dbProgram := &models.Program{}
	result := m.db.Model(dbProgram).Joins("CategorisationType").Where("program.id = ?", id).First(dbProgram)
	if result.Error != nil {
		return nil, fmt.Errorf("error when searching program with id %d: %w", id, result.Error)
	}
	program, err := m.FromDBProgram(dbProgram)
	if err != nil {
		return nil, err
	}
	primaryImage, err := m.FetchImageByID(dbProgram.PrimaryImage)
	if err != nil {
		return nil, err
	}
	program.PrimaryImage = primaryImage
	m.cache.AddProgram(id, program)
	return program, nil
}

func (m *DBDataConvertor) FetchBrandByID(id *int) (*Brand, error) {
	if id == nil || *id == 0 {
		return nil, nil
	}
	brand := m.cache.GetBrand(*id)
	if brand != nil {
		return brand, nil
	}
	dbBrand := &models.Brand{}
	result := m.db.Model(&models.Brand{}).Where("id = ?", *id).First(dbBrand)
	if result.Error != nil {
		return nil, fmt.Errorf("error when searching brand with id %d: %w", id, result.Error)
	}
	brand = m.FromDBBrand(dbBrand)
	primaryImage, err := m.FetchImageByID(dbBrand.PrimaryImageID)
	if err != nil {
		return nil, err
	}
	brand.PrimaryImage = primaryImage
	m.cache.AddBrand(*id, brand)
	return brand, nil
}

func (m *DBDataConvertor) FetchOwningService(programID int) (*Service, error) {
	if programID == 0 {
		return nil, nil
	}
	service := m.cache.GetOwningService(programID)
	if service != nil {
		return service, nil
	}
	dbServices := []models.Service{}
	result := m.db.Model(&models.Service{}).Joins("INNER JOIN program_has_owning_service p_os ON p_os.service=service.id").Where("p_os.program = ?", programID).Order("service.id").Limit(1).Find(&dbServices)
	if result.Error != nil {
		return nil, fmt.Errorf("error when searching owning service of program %d: %w", programID, result.Error)
	}
	if len(dbServices) == 0 {
		return nil, nil
	}
	service, err := m.FromDBService(&dbServices[0])
	if err != nil {
		return nil, err
	}
	m.cache.AddOwningService(programID, service)
	return service, nil
}

func (m *DBDataConvertor) FromDBService(dbService *models.Service) (*Service, error) {
	if dbService == nil {
		return nil, nil
	}
	return &Service{
		ARID:            dbService.ARID,
		Title:           dbService.Title,
		ServiceID:       dbService.ServiceID,
		LastUpdatedUTC:  time.Time(dbService.LastUpdatedUTC),
		ParentServiceID: dbService.ParentServiceID,
	}, nil
}

func (m *DBDataConvertor) UpdateLastUpdate(item *ProgramItem, lastUpdate time.Time) {
	date := lastUpdate.UTC().Format(DateFormat)
	if item.LastPublishedDate > date {
		return
	}
	item.LastPublishedDate = date
}
