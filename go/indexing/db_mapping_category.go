package indexing

import (
	"fmt"

	"gorm.io/gorm"

	base "stash.abc-dev.net.au/rmp/rmp-program-api"
	"stash.abc-dev.net.au/rmp/rmp-program-api/models"
)

func (m *DBDataConvertor) CacheCategories() error {
	categories := []*models.Category{}
	result := m.db.Model(&models.Category{}).Find(&categories)
	if result.Error != nil {
		return fmt.Errorf("error when cache categories: %w", result.Error)
	}
	for _, category := range categories {
		m.cache.AddCategory(category.ID, m.FromDBCategory(category))
	}
	for _, category := range categories {
		if category.ParentCategoryID == 0 {
			continue
		}
		parent := m.cache.GetCategory(category.ParentCategoryID)
		if parent == nil {
			continue
		}
		cached := m.cache.GetCategory(category.ID)
		cached.Parent = parent
	}
	return nil
}
func (m *DBDataConvertor) FetchCategories(parent any) ([]*Category, error) {
	categories := make([]*Category, 0, 10)
	var result *gorm.DB
	programID := 0
	switch v := parent.(type) {
	case *models.Program:
		result = m.db.Model(&models.Subject{}).Joins("INNER JOIN program_has_subject phs ON phs.subject=subject.id").Where("phs.program = ?", v.ID)
	case *models.Episode:
		if v.Series != nil && v.Series.PartOfProgram != nil {
			program := m.cache.GetProgram(*v.Series.PartOfProgram)
			if program.CategorisationType == models.CategorisationTypeUnion {
				programCategories, err := m.FetchCategories(&models.Program{ID: *v.Series.PartOfProgram})
				if err != nil {
					return nil, err
				}
				categories = append(categories, programCategories...)
			} else if program.CategorisationType == models.CategorisationTypeOverride {
				return m.FetchCategories(&models.Program{ID: *v.Series.PartOfProgram})
			} else {
				programID = *v.Series.PartOfProgram
			}
		}
		result = m.db.Model(&models.Subject{}).Joins("INNER JOIN episode_has_subject ehs ON ehs.subject=subject.id").Where("ehs.episode = ?", v.ID)
	case *models.Segment:
		if v.ProgramID != nil {
			program := m.cache.GetProgram(*v.ProgramID)
			if program.CategorisationType == models.CategorisationTypeUnion {
				programCategories, err := m.FetchCategories(&models.Program{ID: *v.ProgramID})
				if err != nil {
					return nil, err
				}
				categories = append(categories, programCategories...)
			} else if program.CategorisationType == models.CategorisationTypeOverride {
				return m.FetchCategories(&models.Program{ID: *v.ProgramID})
			} else {
				programID = *v.ProgramID
			}
		}
		result = m.db.Model(&models.Subject{}).Joins("INNER JOIN segment_has_subject shs ON shs.subject=subject.id").Where("shs.segment = ?", v.ID)
	default:
		return nil, fmt.Errorf("unsupported FetchCategories type %T", parent)
	}
	subjects := make([]*models.Subject, 0, 10)
	result.Find(&subjects)
	if result.Error != nil {
		return nil, result.Error
	}
	for _, subject := range subjects {
		category := m.cache.GetCategory(subject.InCategory)
		if category == nil {
			continue
		}
		categories = append(categories, category)
	}
	if len(categories) == 0 && programID != 0 {
		return m.FetchCategories(&models.Program{ID: programID})
	}
	removeDuplicates := make([]*Category, 0, 10)
	keys := base.NewSet[string]()
	for _, cat := range categories {
		if keys.Contains(cat.ARID) {
			continue
		}
		keys.Add(cat.ARID)
		removeDuplicates = append(removeDuplicates, cat)
	}
	return removeDuplicates, nil
}

func (m *DBDataConvertor) FromDBCategory(c *models.Category) *Category {
	if c == nil {
		return nil
	}
	return &Category{
		ARID:  c.ARID,
		Label: c.CategoryLabel,
	}
}
