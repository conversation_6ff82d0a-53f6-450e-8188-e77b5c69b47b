package indexing

import (
	"context"
	"crypto/tls"
	"net/http"

	"github.com/aws/aws-sdk-go-v2/config"
	srch "github.com/opensearch-project/opensearch-go/v4"
	"github.com/opensearch-project/opensearch-go/v4/opensearchapi"
	requestsigner "github.com/opensearch-project/opensearch-go/v4/signer/awsv2"

	base "stash.abc-dev.net.au/rmp/rmp-program-api"
)

func ConnectToSearch(config base.IndexingConfiguration) (*opensearchapi.Client, error) {
	host := config.URL
	if base.TaskInfo() != nil {
		return connectToAWSCluster(host)
	} else {
		return connectWithAuth(host, config.User, config.Password)
	}
}

func connectWithAuth(host string, user string, password string) (*opensearchapi.Client, error) {
	return opensearchapi.NewClient(opensearchapi.Config{
		Client: srch.Config{
			Transport: &http.Transport{
				TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
			},
			Addresses: []string{host},
			Username:  user,
			Password:  password,
		},
	})
}

func connectToAWSCluster(host string) (*opensearchapi.Client, error) {
	ctx := context.Background()
	awsCfg, err := config.LoadDefaultConfig(ctx,
		config.WithRegion(base.StringEnvOrDefault("AWS_REGION", "ap-southeast-2")),
	)
	if err != nil {
		return nil, err
	}
	signer, err := requestsigner.NewSignerWithService(awsCfg, "es")
	if err != nil {
		panic(err)
	}
	return opensearchapi.NewClient(opensearchapi.Config{
		Client: srch.Config{
			Addresses: []string{host},
			Signer:    signer,
		},
	})
}
