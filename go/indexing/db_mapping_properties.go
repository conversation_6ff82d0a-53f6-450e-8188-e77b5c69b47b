package indexing

import (
	"fmt"
	"time"

	"gorm.io/gorm"

	"stash.abc-dev.net.au/rmp/rmp-program-api/models"
)

func (m *DBDataConvertor) FetchProperties(parent any) ([]*Property, error) {
	propertyCondition := `property.value=1 
	AND (ISNULL(property.start_utc) OR property.start_utc <= NOW()) 
	AND (ISNULL(property.start_utc) OR property.end_utc >= NOW()) 
	AND property.api_user IN ?`
	properties := make([]*Property, 0, 10)
	dbProperties := []*models.Property{}
	var result *gorm.DB
	switch v := parent.(type) {
	case *models.Program:
		programProperties := m.cache.GetProgramProperties(v.ID)
		if programProperties != nil {
			return programProperties, nil
		}
		result = m.db.Model(&models.Property{}).Joins("Type").
			Joins("INNER JOIN program_has_properties phs ON phs.property_id=property.id").
			Where(propertyCondition+" AND phs.program_id = ?", m.cache.APIUserIDs(), v.ID).Find(&dbProperties)
		if result.Error != nil {
			return nil, fmt.Errorf("error when searching properties of program %d: %w", v.ID, result.Error)
		}
	case *models.Episode:
		if v.Series != nil && v.Series.PartOfProgram != nil {
			programProperties, err := m.FetchProperties(&models.Program{ID: *v.Series.PartOfProgram})
			if err != nil {
				return nil, err
			}
			properties = append(properties, programProperties...)
		}
		result = m.db.Model(&models.Property{}).Joins("Type").
			Joins("INNER JOIN episode_has_properties ehs ON ehs.property_id=property.id").
			Where(propertyCondition+" AND ehs.episode_id = ?", m.cache.APIUserIDs(), v.ID).Find(&dbProperties)
		if result.Error != nil {
			return nil, fmt.Errorf("error when searching properties of program %d: %w", v.ID, result.Error)
		}
	case *models.Segment:
		if v.ProgramID != nil {
			programProperties, err := m.FetchProperties(&models.Program{ID: *v.ProgramID})
			if err != nil {
				return nil, err
			}
			properties = append(properties, programProperties...)
		}
		result = m.db.Model(&models.Property{}).Joins("Type").
			Joins("INNER JOIN segment_has_properties shs ON shs.property_id=property.id").
			Where(propertyCondition+" AND shs.segment_id = ?", m.cache.APIUserIDs(), v.ID).Find(&dbProperties)
		if result.Error != nil {
			return nil, fmt.Errorf("error when searching properties of program %d: %w", v.ID, result.Error)
		}
	default:
		return nil, fmt.Errorf("unsupported FetchCategories type %T", parent)
	}
	for _, dbProperty := range dbProperties {
		property := m.FromDBProperty(dbProperty)
		properties = append(properties, property)
	}
	if v, ok := parent.(*models.Program); ok {
		m.cache.AddProgramProperties(v.ID, properties)
	}
	return properties, nil
}

func (m *DBDataConvertor) FromDBProperty(p *models.Property) *Property {
	if p == nil {
		return nil
	}
	apiUser := m.cache.GetAPIUser(p.APIUser)
	var active map[string]map[string]string = nil
	if p.StartUTC != nil && p.EndUTC != nil {
		active = map[string]map[string]string{
			apiUser: {
				"startUtc": p.StartUTC.Format(DateFormat),
				"endUtc":   p.EndUTC.Format(DateFormat),
			},
		}
	}
	return &Property{
		Type: p.Type.Title,
		User: []string{
			apiUser,
		},
		CustomValue: map[string]*string{
			apiUser: p.Custom,
		},
		LastUpdatedUTC: time.Time(p.LastUpdatedUTC),
		Value:          true,
		Active:         active,
	}
}
