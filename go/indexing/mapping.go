package indexing

import (
	"bytes"
	"context"
	"fmt"
	"log/slog"
	"net/http"
	"os"
	"strings"

	jsoniter "github.com/json-iterator/go"
	"github.com/opensearch-project/opensearch-go/v4/opensearchapi"
)

var json = jsoniter.ConfigCompatibleWithStandardLibrary

const (
	IndexProgramItem = "programitem-go"
	IndexLive        = "live-go"
)

const (
	EntityEpisode      = "Episode"
	EntitySegment      = "Segment"
	EntityImage        = "Image"
	EntityImageResized = "ImageResized"
	EntityOutlet       = "Outlet"
)

func RebuildMapping(client *opensearchapi.Client, path string, indices ...string) error {
	settings, mappings, err := loadMappings(path, indices)
	if err != nil {
		return err
	}
	// Delete indices
	resp, err := client.Indices.Delete(context.Background(), opensearchapi.IndicesDeleteReq{
		Indices: indices,
	})
	if err != nil {
		if resp.Inspect().Response.StatusCode != http.StatusNotFound {
			return fmt.Errorf("failed to delete indices: %s", resp.Inspect().Response.String())
		}
	}
	// Create index
	for index, mapping := range mappings {
		if err := CreateIndex(client, index, settings, mapping); err != nil {
			return err
		}
	}
	return nil
}

func CreateIndex(client *opensearchapi.Client, index string, settings jsoniter.RawMessage, mapping jsoniter.RawMessage) error {
	body := make(map[string]jsoniter.RawMessage)
	body["settings"] = settings
	body["mappings"] = mapping
	b, err := json.Marshal(body)
	if err != nil {
		return fmt.Errorf("failed to marshal body: %w", err)
	}
	_, err = client.Indices.Create(context.Background(), opensearchapi.IndicesCreateReq{
		Index: index,
		Body:  bytes.NewReader(b),
	})
	if err != nil {
		return fmt.Errorf("failed to create index %s: %w", index, err)
	}
	slog.Info("created index", "name", index)
	return nil
}

func CreateIndexIfNotExists(client *opensearchapi.Client, path string, indices ...string) error {
	slog.Info("create index if not exists", "path", path, "indices", indices)
	settings, mappings, err := loadMappings(path, indices)
	if err != nil {
		return err
	}
	for _, index := range indices {
		resp, err := client.Indices.Exists(context.Background(), opensearchapi.IndicesExistsReq{Indices: []string{index}})
		if err == nil {
			continue
		}
		if resp == nil || resp.StatusCode != http.StatusNotFound {
			return fmt.Errorf("failed to check if indices exist: %T %w", err, err)
		}
		if err := CreateIndex(client, index, settings, mappings[index]); err != nil {
			return err
		}
	}
	return nil
}

func loadMappings(path string, indices []string) (jsoniter.RawMessage, map[string]jsoniter.RawMessage, error) {
	root := &root{}
	rootBytes, err := os.ReadFile(path + "/opensearch_mapping.json")
	if err != nil {
		return nil, nil, fmt.Errorf("failed to load opensearch_mapping.json: %w", err)
	}
	err = json.Unmarshal(rootBytes, root)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to unmarshal opensearch_mapping.json: %w", err)
	}
	mappings := make(map[string]jsoniter.RawMessage)
	programItems, err := os.ReadFile(path + "/opensearch_mapping.programitem.json")
	if err != nil {
		return nil, nil, fmt.Errorf("failed to load opensearch_mapping.programitem.json: %w", err)
	}
	for _, index := range indices {
		switch index {
		case IndexProgramItem:
			mappings[IndexProgramItem] = programItems
		case IndexLive:
			live, err := os.ReadFile(path + "/opensearch_mapping.live.json")
			if err != nil {
				return nil, nil, fmt.Errorf("failed to load opensearch_mapping.live.json: %w", err)
			}
			mappings[IndexLive] = jsoniter.RawMessage(strings.Replace(string(live), "\"$programitem\"", string(programItems), -1))
		}
	}
	return root.Body.Settings, mappings, nil
}

type root struct {
	Body struct {
		Settings jsoniter.RawMessage `json:"settings"`
		Mappings map[string]string   `json:"mappings"`
	} `json:"body"`
}
