package indexing

import (
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"
	"stash.abc-dev.net.au/rmp/rmp-program-api/models"
)

func (m *DBDataConvertor) FromDBWebpage(w *models.Webpage) *WebPage {
	if w == nil {
		return nil
	}
	return &WebPage{
		ARID:           w.ARID,
		Title:          w.Title,
		URL:            w.URL,
		LastUpdatedUTC: time.Time(w.LastUpdatedUTC),
	}
}

func (m *DBDataConvertor) FetchImageByID(id *int) (*Image, error) {
	if id == nil || *id == 0 {
		return nil, nil
	}
	image := m.cache.GetImage(*id)
	if image != nil {
		return image, nil
	}
	dbImage := &models.Image{}
	result := m.db.Model(&models.Image{}).Joins("Role").Where("image.id = ?", id).First(dbImage)
	if result.Error != nil {
		return nil, fmt.Errorf("error when searching image with id %d: %w", id, result.Error)
	}
	err := m.FetchImageSizes(dbImage)
	if err != nil {
		return nil, err
	}
	img := m.AssembleImage(dbImage)
	m.cache.AddImage(*id, img)
	return img, nil
}

func (m *DBDataConvertor) FetchLinkedImages(parent any) ([]*Image, error) {
	var tx *gorm.DB
	switch v := parent.(type) {
	case *models.Program:
		tx = m.db.Model(&models.Image{}).Joins("Role").Joins("INNER JOIN program_has_image pi ON pi.image=image.id").Where("pi.program = ?", v.ID)
	case *models.Episode:
		tx = m.db.Model(&models.Image{}).Joins("Role").Joins("INNER JOIN episode_has_image pi ON pi.image=image.id").Where("pi.episode = ?", v.ID)
	case *models.Segment:
		tx = m.db.Model(&models.Image{}).Joins("Role").Joins("INNER JOIN segment_has_image pi ON pi.image=image.id").Where("pi.segment = ?", v.ID)
	case *models.Series:
		tx = m.db.Model(&models.Image{}).Joins("Role").Joins("INNER JOIN series_has_image pi ON pi.image=image.id").Where("pi.series = ?", v.ID)
	case *models.Person:
		tx = m.db.Model(&models.Image{}).Joins("Role").Joins("INNER JOIN person_has_image pi ON pi.image=image.id").Where("pi.person = ?", v.ID)
	default:
		return nil, fmt.Errorf("unsupported FetchLinkedImages type %T", parent)
	}
	images := []*models.Image{}
	result := tx.Find(&images)
	if result.Error != nil {
		return nil, result.Error
	}
	imgs := make([]*Image, len(images))
	for i, dbImage := range images {
		image := m.cache.GetImage(dbImage.ID)
		if image != nil {
			imgs[i] = image
			continue
		}
		err := m.FetchImageSizes(dbImage)
		if err != nil {
			return nil, err
		}
		imgs[i] = m.AssembleImage(dbImage)
		m.cache.AddImage(dbImage.ID, imgs[i])
	}
	return imgs, nil
}

func (m *DBDataConvertor) FetchImageSizes(image *models.Image) error {
	resized := []*models.ImageResized{}
	result := m.db.Model(&models.ImageResized{}).InnerJoins("Size").Where("image_id = ?", image.ID).Find(&resized)
	if result.Error != nil {
		return result.Error
	}
	image.Resized = resized
	return nil
}

func (m *DBDataConvertor) AssembleImage(image *models.Image) *Image {
	img := &Image{
		Entity: EntityImage,
		ARID:   image.ARID,
		Title:  image.Title,
		URL:    strings.ReplaceAll(image.URL, "\\", ""),
		Width:  image.Width,
		Height: image.Height,
		Source: image.Source,
		Sizes:  make([]ImageSize, 0),
	}
	if image.Role != nil {
		img.Role = image.Role.Role
	}
	for _, resized := range image.Resized {
		img.Sizes = append(img.Sizes, ImageSize{
			Entity:      EntityImageResized,
			ARID:        resized.ARID,
			URL:         strings.ReplaceAll(resized.URL, "\\", ""),
			AspectRatio: resized.Size.AspectRatio,
			Width:       resized.Size.Width,
			Height:      resized.Size.Height,
		})
	}
	return img
}
