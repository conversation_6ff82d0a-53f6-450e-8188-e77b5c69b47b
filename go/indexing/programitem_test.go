package indexing

import (
	"fmt"
	"log/slog"
	"os"
	"runtime/pprof"
	"testing"
	"time"

	slogorm "github.com/orandin/slog-gorm"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	"stash.abc-dev.net.au/rmp/rmp-program-api/models"
)

func BenchmarkConvertProgramItemFromEpisode(b *testing.B) {
	convertor := initEnvironment()
	dbEpisode := &models.Episode{}
	result := convertor.db.Model(&models.Episode{}).
		Joins("PrimaryWebpage").
		Joins("Series").
		Joins("Segment").
		Joins("Segment.PrimaryWebpage").
		Where("episode.arid = ?", "peWDEzGY83").First(dbEpisode)
	if result.Error != nil {
		b.Fatalf("failed to get episode: %s", result.Error.Error())
	}
	b.<PERSON>setTimer()
	f, err := os.Create("cpu.prof")
	if err != nil {
		b.Fatalf("failed to create cpu profile: %s", err.Error())
	}
	pprof.StartCPUProfile(f)
	defer pprof.StopCPUProfile()
	for i := 0; i < b.N; i++ {
		programItem, err := convertor.FromDBEpisode(dbEpisode)
		if err != nil {
			b.Fatalf("failed to convert episode to program item: %s", err.Error())
		}
		if programItem.Entity != "Episode" {
			b.Fatalf("expected entity to be episode, got %s", programItem.Entity)
		}
	}
}

func initEnvironment() *DBDataConvertor {
	glogger := slogorm.New(
		slogorm.WithHandler(slog.NewJSONHandler(os.Stdout, nil)),
		slogorm.WithSlowThreshold(time.Second),
		slogorm.SetLogLevel(slogorm.ErrorLogType, slog.LevelError),
		slogorm.SetLogLevel(slogorm.SlowQueryLogType, slog.LevelWarn),
		slogorm.SetLogLevel(slogorm.DefaultLogType, slog.LevelInfo),
	)
	connstring := "papi:password@tcp(localhost:3308)/papi_dev?charset=utf8mb4&parseTime=True&loc=Local"
	db, err := gorm.Open(mysql.Open(connstring), &gorm.Config{
		PrepareStmt:            true,
		SkipDefaultTransaction: true,
		DisableAutomaticPing:   true,
		Logger:                 glogger,
	})
	if err != nil {
		panic(fmt.Sprintf("failed to connect database %s: %s", connstring, err.Error()))
	}
	sqlDB, err := db.DB()
	if err != nil {
		panic(err)
	}

	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	return NewDBDataConvertor(db)
}
