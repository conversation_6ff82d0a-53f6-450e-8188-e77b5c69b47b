package indexing

import (
	cmap "github.com/orcaman/concurrent-map/v2"

	"stash.abc-dev.net.au/rmp/rmp-program-api/models"
)

type IndexItemCache struct {
	programs            cmap.ConcurrentMap[int, *Program]
	images              cmap.ConcurrentMap[int, *Image]
	brands              cmap.ConcurrentMap[int, *Brand]
	owningServices      cmap.ConcurrentMap[int, *Service]
	presenters          cmap.ConcurrentMap[int, *Person]
	categories          cmap.ConcurrentMap[int, *Category]
	programProperties   cmap.ConcurrentMap[int, []*Property]
	apiUsers            cmap.ConcurrentMap[int, string]
	outlets             cmap.ConcurrentMap[int, *models.Outlet]
	childServiceOutlets cmap.ConcurrentMap[int, []*models.Outlet]
}

func NewIndexItemCache() *IndexItemCache {
	return &IndexItemCache{
		programs:            cmap.NewWithCustomShardingFunction[int, *Program](sharding),
		images:              cmap.NewWithCustomShardingFunction[int, *Image](sharding),
		brands:              cmap.NewWithCustomShardingFunction[int, *Brand](sharding),
		owningServices:      cmap.NewWithCustomShardingFunction[int, *Service](sharding),
		presenters:          cmap.NewWithCustomShardingFunction[int, *Person](sharding),
		categories:          cmap.NewWithCustomShardingFunction[int, *Category](sharding),
		programProperties:   cmap.NewWithCustomShardingFunction[int, []*Property](sharding),
		apiUsers:            cmap.NewWithCustomShardingFunction[int, string](sharding),
		outlets:             cmap.NewWithCustomShardingFunction[int, *models.Outlet](sharding),
		childServiceOutlets: cmap.NewWithCustomShardingFunction[int, []*models.Outlet](sharding),
	}
}

func (c *IndexItemCache) AddProgram(id int, p *Program) {
	c.programs.Set(id, p)
}

func (c *IndexItemCache) GetProgram(id int) *Program {
	p, ok := c.programs.Get(id)
	if !ok {
		return nil
	}
	return p
}

func (c *IndexItemCache) AddImage(id int, image *Image) {
	c.images.Set(id, image)
}

func (c *IndexItemCache) GetImage(id int) *Image {
	i, ok := c.images.Get(id)
	if !ok {
		return nil
	}
	return i
}

func (c *IndexItemCache) AddBrand(id int, brand *Brand) {
	c.brands.Set(id, brand)
}

func (c *IndexItemCache) GetBrand(id int) *Brand {
	b, ok := c.brands.Get(id)
	if !ok {
		return nil
	}
	return b
}

func (c *IndexItemCache) AddOwningService(id int, service *Service) {
	c.owningServices.Set(id, service)
}

func (c *IndexItemCache) GetOwningService(id int) *Service {
	s, ok := c.owningServices.Get(id)
	if !ok {
		return nil
	}
	return s
}

func (c *IndexItemCache) AddPresenter(id int, presenter *Person) {
	c.presenters.Set(id, presenter)
}

func (c *IndexItemCache) GetPresenter(id int) *Person {
	p, ok := c.presenters.Get(id)
	if !ok {
		return nil
	}
	return p
}

func sharding(key int) uint32 {
	return uint32(key)
}

func (c *IndexItemCache) AddCategory(id int, category *Category) {
	c.categories.Set(id, category)
}

func (c *IndexItemCache) GetCategory(id int) *Category {
	cat, ok := c.categories.Get(id)
	if !ok {
		return nil
	}
	return cat
}

func (c *IndexItemCache) AddProgramProperties(programID int, properties []*Property) {
	c.programProperties.Set(programID, properties)
}

func (c *IndexItemCache) GetProgramProperties(programID int) []*Property {
	p, ok := c.programProperties.Get(programID)
	if !ok {
		return nil
	}
	return p
}

func (c *IndexItemCache) AddAPIUser(id int, user *models.APIUser) {
	c.apiUsers.Set(id, user.Username)
}

func (c *IndexItemCache) GetAPIUser(id int) string {
	u, ok := c.apiUsers.Get(id)
	if !ok {
		return ""
	}
	return u
}

func (c *IndexItemCache) APIUserIDs() []int {
	return c.apiUsers.Keys()
}

func (c *IndexItemCache) AddOutlet(id int, outlet *models.Outlet) {
	c.outlets.Set(id, outlet)
}

func (c *IndexItemCache) GetOutlet(id int) *models.Outlet {
	o, ok := c.outlets.Get(id)
	if !ok {
		return nil
	}
	return o
}

func (c *IndexItemCache) AddChildServiceOutlet(serviceID int, outlet *models.Outlet) {
	childOutlets, ok := c.childServiceOutlets.Get(serviceID)
	if !ok {
		childOutlets = []*models.Outlet{}
		c.childServiceOutlets.Set(serviceID, childOutlets)
	}
	c.childServiceOutlets.Set(serviceID, append(childOutlets, outlet))
}

func (c *IndexItemCache) GetChildServiceOutlets(serviceID int) []*models.Outlet {
	o, ok := c.childServiceOutlets.Get(serviceID)
	if !ok {
		return nil
	}
	return o
}
