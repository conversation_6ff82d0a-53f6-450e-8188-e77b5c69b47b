package indexing

import (
	"time"
)

const DateFormat = "2006-01-02T15:04:05-0700"

type ProgramItem struct {
	Entity                  string            `json:"entity,omitempty"`
	ARID                    string            `json:"arid,omitempty"`
	Title                   string            `json:"title"`
	MediumSynopsis          *string           `json:"medium_synopsis"`
	ShortSynopsis           *string           `json:"short_synopsis"`
	MiniSynopsis            *string           `json:"mini_synopsis"`
	ExcludeFromIndex        bool              `json:"exclude_from_index"`
	Program                 *Program          `json:"program"`
	Series                  *Series           `json:"series"`
	CreatingBrand           *Brand            `json:"creating_brand,omitempty"`
	CreatingService         *Service          `json:"creating_service,omitempty"`
	PrimaryImage            *Image            `json:"primary_image,omitempty"`
	Images                  []*Image          `json:"images"`
	PrimaryWebPage          *WebPage          `json:"primary_webpage,omitempty"`
	Categories              []*Category       `json:"categories"`
	Presenters              []*Person         `json:"presenters"`
	Live                    []*Live           `json:"live"`
	OnDemand                *OnDemand         `json:"ondemand,omitempty"`
	PrimaryPublicationEvent *PublicationEvent `json:"primary_publication_event"`
	LastPublishedDate       string            `json:"last_published_date,omitempty"`
	ServiceGUID             string            `json:"service_guid,omitempty"`
	Properties              []*Property       `json:"properties"`
	ChildSegments           []ChildSegment    `json:"child_segments"`
	PartOfEpisode           *Episode          `json:"partof_episode,omitempty"`
}

type ChildSegment struct {
	ARID  string `json:"arid,omitempty"`
	Title string `json:"title,omitempty"`
}

type Program struct {
	ARID                  string    `json:"arid"`
	Title                 string    `json:"title,omitempty"`
	ShortSynopsis         string    `json:"short_synopsis,omitempty"`
	MiniSynopsis          string    `json:"mini_synopsis,omitempty"`
	PrimaryContactDetails string    `json:"primary_contact_details,omitempty"`
	PrimaryEmailAddress   string    `json:"primary_email_address,omitempty"`
	PrimaryMobile         string    `json:"primary_mobile,omitempty"`
	PrimarySMS            string    `json:"primary_sms,omitempty"`
	PrimaryTelephone      string    `json:"primary_telephone,omitempty"`
	ABCRadioAirportCode   string    `json:"abcradio_airport_code,omitempty"`
	PrimaryImage          *Image    `json:"primary_image,omitempty"`
	Images                []*Image  `json:"images,omitempty"`
	CreatingBrand         *Brand    `json:"-"`
	OwningService         *Service  `json:"-"`
	LastUpdatedUTC        time.Time `json:"-"`
	CategorisationType    string    `json:"-"`
}

type Live struct {
	ARID             string    `json:"arid,omitempty"`
	VersionType      string    `json:"version_type,omitempty"`
	Start            string    `json:"start,omitempty"`
	End              string    `json:"end,omitempty"`
	DurationSeconds  int       `json:"duration_seconds,omitempty"`
	DefiningTimezone *string   `json:"defining_timezone"`
	ScheduleType     string    `json:"schedule_type,omitempty"`
	Outlets          []*Outlet `json:"outlets,omitempty"`
	LastUpdatedUTC   time.Time `json:"-"`
}

type Outlet struct {
	Entity         string         `json:"entity,omitempty"`
	ARID           string         `json:"arid,omitempty"`
	Title          string         `json:"title,omitempty"`
	Type           string         `json:"type,omitempty"`
	AudioStreams   []*Audio       `json:"audio_streams,omitempty"`
	PartOfService  *OutletService `json:"partof_service,omitempty"`
	LastUpdatedUTC time.Time      `json:"-"`
}

type OutletService struct {
	ARID      string `json:"arid,omitempty"`
	Title     string `json:"title,omitempty"`
	ServiceID string `json:"service_id,omitempty"`
}

type Audio struct {
	ARID           string    `json:"arid,omitempty"`
	Type           string    `json:"type,omitempty"`
	Title          string    `json:"title,omitempty"`
	URL            string    `json:"url,omitempty"`
	SizeBytes      int64     `json:"size_bytes,omitempty"`
	LastUpdatedUTC time.Time `json:"-"`
}

type Series struct {
	ARID           string    `json:"arid,omitempty"`
	Title          string    `json:"title"`
	LastUpdatedUTC time.Time `json:"-"`
}

type Brand struct {
	ARID           string    `json:"arid,omitempty"`
	Title          string    `json:"title,omitempty"`
	BrandID        string    `json:"brand_id,omitempty"`
	PrimaryImage   *Image    `json:"primary_image,omitempty"`
	LastUpdatedUTC time.Time `json:"-"`
}
type Service struct {
	ARID                  string     `json:"arid,omitempty"`
	Title                 string     `json:"title,omitempty"`
	ServiceID             string     `json:"service_id,omitempty"`
	PrimaryContactDetails string     `json:"primary_contact_details,omitempty"`
	PrimaryEmailAddress   string     `json:"primary_email_address,omitempty"`
	PrimaryMobile         string     `json:"primary_mobile,omitempty"`
	PrimarySMS            string     `json:"primary_sms,omitempty"`
	PrimaryTelephone      string     `json:"primary_telephone,omitempty"`
	ParentServiceID       int        `json:"-"`
	LastUpdatedUTC        time.Time  `json:"-"`
	ChildServices         []*Service `json:"-"`
}

type WebPage struct {
	ARID           string    `json:"arid,omitempty"`
	Title          string    `json:"title,omitempty"`
	URL            string    `json:"url"`
	LastUpdatedUTC time.Time `json:"-"`
}

type Category struct {
	ARID           string    `json:"arid,omitempty"`
	Label          string    `json:"label,omitempty"`
	Parent         *Category `json:"parent_category,omitempty"`
	LastUpdatedUTC time.Time `json:"-"`
}

type Person struct {
	ARID           string    `json:"arid,omitempty"`
	DisplayName    string    `json:"display_name,omitempty"`
	PrimaryImage   *Image    `json:"primary_image,omitempty"`
	LastUpdatedUTC time.Time `json:"-"`
}

type Image struct {
	Entity         string      `json:"entity,omitempty"`
	ARID           string      `json:"arid,omitempty"`
	Title          string      `json:"title,omitempty"`
	URL            string      `json:"url,omitempty"`
	Width          int         `json:"width,omitempty"`
	Height         int         `json:"height,omitempty"`
	Role           string      `json:"role,omitempty"`
	Source         string      `json:"source,omitempty"`
	Sizes          []ImageSize `json:"sizes"`
	LastUpdatedUTC time.Time   `json:"-"`
}

type ImageSize struct {
	Entity      string `json:"entity,omitempty"`
	ARID        string `json:"arid,omitempty"`
	URL         string `json:"url,omitempty"`
	AspectRatio string `json:"aspect_ratio,omitempty"`
	Width       int    `json:"width,omitempty"`
	Height      int    `json:"height,omitempty"`
}

type Episode struct {
	ARID           string `json:"arid,omitempty"`
	Title          string `json:"title"`
	MediumSynopsis string `json:"medium_synopsis,omitempty"`
	ShortSynopsis  string `json:"short_synopsis,omitempty"`
	MiniSynopsis   string `json:"mini_synopsis,omitempty"`
	PrimaryImage   *Image `json:"primary_image,omitempty"`
}

type Segment struct {
	ARID           string    `json:"arid,omitempty"`
	Title          string    `json:"title,omitempty"`
	LastUpdatedUTC time.Time `json:"-"`
}

type PublicationEvent struct {
	ARID             string    `json:"arid,omitempty"`
	Start            string    `json:"start,omitempty"`
	End              string    `json:"end,omitempty"`
	DefiningTimezone string    `json:"defining_timezone,omitempty"`
	ScheduleType     string    `json:"schedule_type,omitempty"`
	LastUpdatedUTC   time.Time `json:"-"`
}

type Property struct {
	Type           string                       `json:"type,omitempty"`
	User           []string                     `json:"user,omitempty"`
	CustomValue    map[string]*string           `json:"custom_value,omitempty"`
	Value          bool                         `json:"value,omitempty"`
	Active         map[string]map[string]string `json:"active,omitempty"`
	LastUpdatedUTC time.Time                    `json:"-"`
}

type OnDemand struct {
	ARID            string `json:"arid,omitempty"`
	VersionType     string `json:"version_type,omitempty"`
	Start           string `json:"start,omitempty"`
	End             string `json:"end,omitempty"`
	DurationSeconds int    `json:"duration_seconds,omitempty"`
	Audio           *Audio `json:"audio,omitempty"`
}
