package indexing

import (
	"bytes"
	"context"
	"errors"
	"log/slog"
	"net/http"

	jsonitor "github.com/json-iterator/go"
	"github.com/opensearch-project/opensearch-go/v4/opensearchapi"
)

type BulkIndexer struct {
	client *opensearchapi.Client
	buffer *bytes.Buffer
}

func NewBulkIndexer(client *opensearchapi.Client) *BulkIndexer {
	return &BulkIndexer{
		client: client,
		buffer: new(bytes.Buffer),
	}
}

func (bi *BulkIndexer) Index(index, id string, item any) (bool, error) {
	bi.buffer.WriteString(`{"index":{"_index":"` + index + `","_id":"` + id + `"}}` + "\n")
	data, err := json.Marshal(item)
	if err != nil {
		panic(err)
	}
	bi.buffer.Write(data)
	bi.buffer.WriteRune('\n')
	if bi.BufferFull() {
		return true, bi.Flush()
	}
	return false, nil
}

func (bi *BulkIndexer) BufferFull() bool {
	return bi.buffer.Len() > 1024*1024*5
}

func (bi *BulkIndexer) Flush() error {
	data := bi.buffer.Bytes()
	resp, err := bi.client.Bulk(context.Background(), opensearchapi.BulkReq{
		Body: bi.buffer,
	})
	if err != nil {
		return err
	}
	status := resp.Inspect().Response.StatusCode
	body := resp.Inspect().Response.Body
	defer body.Close()
	if status != http.StatusOK {
		slog.Error("bulk index error", "status", status, "data", jsonitor.RawMessage(data))
		return errors.New(http.StatusText(status))
	}
	parsedResp := &bulkIndexResp{}
	err = json.NewDecoder(body).Decode(parsedResp)
	if err != nil {
		return err
	}
	bi.buffer.Reset()
	if parsedResp.Errors {
		for _, item := range parsedResp.Items {
			for action, value := range item {
				if value.Error == nil {
					continue
				}
				slog.Error("bulk index error", "action", action, "index", value.Index, "id", value.ID, "reason", value.Error.Reason)
			}
		}
	}
	return nil
}

type bulkIndexResp struct {
	Took   int64                          `json:"took"`
	Errors bool                           `json:"errors"`
	Items  []map[string]bulkIndexRespItem `json:"items"`
}

type bulkIndexRespItem struct {
	Index   string              `json:"_index"`
	ID      string              `json:"_id"`
	Status  int                 `json:"status"`
	Result  string              `json:"result"`
	Version int                 `json:"_version"`
	Error   *bulkIndexRespError `json:"error"`
}

type bulkIndexRespError struct {
	Type      string `json:"type"`
	Reason    string `json:"reason"`
	Index     string `json:"index"`
	Shard     string `json:"shard"`
	IndexUUID string `json:"index_uuid"`
}
