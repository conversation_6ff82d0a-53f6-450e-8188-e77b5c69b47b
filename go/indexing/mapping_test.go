package indexing

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestLoadMappings(t *testing.T) {
	settings, mappings, err := loadMappings("/Users/<USER>/projects/abc/radio/rmp-program-api/src/Resources/opensearch", []string{"programitem", "live"})
	assert.NoError(t, err)
	assert.Equal(t, 2, len(mappings))
	assert.Contains(t, settings, "custom_asciifolding")
	programItemIndex := mappings[IndexProgramItem]
	assert.Contains(t, string(programItemIndex), "programitem_arid")
	assert.Contains(t, string(programItemIndex), `"live": {`)
}
