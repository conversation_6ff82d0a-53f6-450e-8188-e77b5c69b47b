package indexing

import (
	"fmt"
	"log/slog"
	"time"

	"gorm.io/gorm"
	base "stash.abc-dev.net.au/rmp/rmp-program-api"
	"stash.abc-dev.net.au/rmp/rmp-program-api/models"
)

func (m *DBDataConvertor) CacheOutlets() error {
	outlets := []*models.Outlet{}
	childServices := make(map[int][]*models.Service)
	result := m.db.Model(&models.Outlet{}).InnerJoins("OutletType").InnerJoins("Service").Where("OutletType.class=?", models.OutletClassLive).Find(&outlets)
	if result.Error != nil {
		return fmt.Errorf("error when cache API users: %w", result.Error)
	}
	for _, outlet := range outlets {
		children, ok := childServices[outlet.Service.ID]
		if !ok {
			var err error
			children, err = m.fetchChildServices(outlet.Service.ID)
			if err != nil {
				return err
			}
			childServices[outlet.Service.ID] = children
		}
		outlet.Service.ChildServices = children
		m.cache.AddOutlet(outlet.ID, outlet)
		if outlet.OutletTypeID == models.OutletTypeLiveStream {
			m.cache.AddChildServiceOutlet(outlet.Service.ID, outlet)
		}
	}
	return nil
}

func (m *DBDataConvertor) fetchChildServices(serviceID int) ([]*models.Service, error) {
	services := []*models.Service{}
	result := m.db.Model(&models.Service{}).Where("parent_service_id = ?", serviceID).Find(&services)
	if result.Error != nil {
		return nil, fmt.Errorf("error when searching child services of service %d: %w", serviceID, result.Error)
	}
	return services, nil
}

func (m *DBDataConvertor) FetchPublicationEvents(parent any) ([]*Live, *PublicationEvent, error) {
	var err error
	events := []*models.PublicationEvent{}
	primaryPublicationEventID := 0
	var result *gorm.DB
	switch v := parent.(type) {
	case *models.Episode:
		result = m.db.Model(&models.PublicationEvent{}).Joins("OutletLink").Joins("ScheduleType").InnerJoins("Version").Joins("Version.VersionType").Where("of_episode = ?", v.ID).Order("publication_event.id, OutletLink.outlet").Find(&events)
		if result.Error != nil {
			return nil, nil, fmt.Errorf("error when searching publication event of episode %s: %w", v.ARID, result.Error)
		}
		if v.PrimaryPublicationEventID != nil {
			primaryPublicationEventID = *v.PrimaryPublicationEventID
		}
	case *models.Segment:
		result = m.db.Model(&models.PublicationEvent{}).Joins("OutletLink").Joins("ScheduleType").InnerJoins("Version").Joins("Version.VersionType").Where("of_segment = ?", v.ID).Order("publication_event.id, OutletLink.outlet").Find(&events)
		if result.Error != nil {
			return nil, nil, fmt.Errorf("error when searching publication event of segment %s: %w", v.ARID, result.Error)
		}
		if v.PrimaryPublicationEventID != nil {
			primaryPublicationEventID = *v.PrimaryPublicationEventID
		}
	default:
		return nil, nil, fmt.Errorf("unsupported FetchPublicationEvents type %T", parent)
	}
	var primaryEvent *PublicationEvent
	lives := make([]*Live, 0, 10)
	fetchedEvents := make(map[string]*models.PublicationEvent)
	eventKey := func(event *models.PublicationEvent, childServiceID string) string {
		return event.ARID + ":" + childServiceID
	}
	upsertEventFromCache := func(event *models.PublicationEvent, childServiceID string) (*models.PublicationEvent, bool) {
		key := eventKey(event, childServiceID)
		indexingEvent, ok := fetchedEvents[key]
		if !ok {
			fetchedEvents[key] = event
			return event, false
		}
		return indexingEvent, true
	}
	for _, event := range events {
		indexingEvent, _ := upsertEventFromCache(event, "")
		if indexingEvent.ID == primaryPublicationEventID {
			primaryEvent = m.FromDBPublicationEvent(indexingEvent)
		}
		if indexingEvent.OutletLink == nil {
			continue
		}
		dbOutlet := m.cache.GetOutlet(indexingEvent.OutletLink.OutletID)
		if dbOutlet == nil {
			continue
		}
		indexingEvent.Outlets = append(indexingEvent.Outlets, dbOutlet)
		// add virtual publication events for child services
		if indexingEvent.DefiningTimezone == nil || *indexingEvent.DefiningTimezone == "" {
			// slog.Info("publication event has no defining timezone", "arid", indexingEvent.ARID)
			continue
		}
		if dbOutlet.Service.ParentServiceID > 0 || len(dbOutlet.Service.ChildServices) == 0 {
			continue
		}
		actualTimezone, err := time.LoadLocation(*indexingEvent.DefiningTimezone)
		if err != nil {
			slog.Error("error when loading timezone", "arid", indexingEvent.ARID, "timezone", *indexingEvent.DefiningTimezone, "error", err)
			continue
		}
		for _, childService := range dbOutlet.Service.ChildServices {
			virtualEvent := indexingEvent.Clone()
			virtualEvent.DefiningTimezone = &childService.ServiceTimezone
			childOutlets := m.cache.GetChildServiceOutlets(childService.ID)
			if childOutlets == nil {
				continue
			}
			virtualEvent.Outlets = childOutlets
			exists := false
			virtualEvent, exists = upsertEventFromCache(virtualEvent, childService.ServiceID)
			if exists {
				continue
			}
			if !virtualEvent.DisableSync {
				virtualTimezone, err := time.LoadLocation(childService.ServiceTimezone)
				if err != nil {
					slog.Error("error when loading timezone", "arid", indexingEvent.ARID, "timezone", childService.ServiceTimezone, "error", err)
					continue
				}
				diff, err := timeDiff(actualTimezone, virtualTimezone)
				if err != nil {
					slog.Error("error when calculating time difference", "arid", indexingEvent.ARID, "timezone", childService.ServiceTimezone, "error", err)
					continue
				}
				virtualEvent.StartUTC = indexingEvent.StartUTC.Add(diff)
				virtualEvent.EndUTC = indexingEvent.EndUTC.Add(diff)
			}
		}
	}
	for _, event := range fetchedEvents {
		live := m.GenerateLiveFromDB(event, event.Outlets)
		if live != nil {
			lives = append(lives, live)
		}
	}

	return lives, primaryEvent, err
}

func timeDiff(loc1, loc2 *time.Location) (time.Duration, error) {
	now := time.Now()
	time1 := now.In(loc1)
	time2 := now.In(loc2)

	_, offset1 := time1.Zone()
	_, offset2 := time2.Zone()
	return time.Duration(offset1-offset2) * time.Second, nil
}

func (m *DBDataConvertor) GenerateLiveFromDB(event *models.PublicationEvent, dbOutlets []*models.Outlet) *Live {
	if event == nil || len(dbOutlets) == 0 {
		return nil
	}
	scheduleType := ""
	if event.ScheduleType != nil {
		scheduleType = event.ScheduleType.Title
	}
	lastUpdateUTC := time.Time(event.LastUpdatedUTC)
	outlets := make([]*Outlet, len(dbOutlets))
	for i, dbOutlet := range dbOutlets {
		if lastUpdateUTC.Before(time.Time(dbOutlet.LastUpdatedUTC)) {
			lastUpdateUTC = time.Time(dbOutlet.LastUpdatedUTC)
		}
		outlets[i] = &Outlet{
			Entity: EntityOutlet,
			ARID:   dbOutlet.ARID,
			Title:  dbOutlet.Title,
			Type:   dbOutlet.OutletType.Title,
			PartOfService: &OutletService{
				ARID:      dbOutlet.Service.ARID,
				Title:     dbOutlet.Service.Title,
				ServiceID: dbOutlet.Service.ServiceID,
			},
		}
	}
	versionType := ""
	if event.Version.VersionType != nil {
		versionType = event.Version.VersionType.Title
	}
	return &Live{
		ARID:             event.ARID,
		VersionType:      versionType,
		Start:            event.StartUTC.Format(DateFormat),
		End:              event.EndUTC.Format(DateFormat),
		DefiningTimezone: event.DefiningTimezone,
		DurationSeconds:  event.Version.DurationSeconds,
		LastUpdatedUTC:   lastUpdateUTC,
		ScheduleType:     scheduleType,
		Outlets:          outlets,
	}
}

func (m *DBDataConvertor) FromDBPublicationEvent(event *models.PublicationEvent) *PublicationEvent {
	if event == nil {
		return nil
	}
	scheduleType := ""
	if event.ScheduleType != nil {
		scheduleType = event.ScheduleType.Title
	}
	return &PublicationEvent{
		ARID:             event.ARID,
		Start:            event.StartUTC.Format(DateFormat),
		End:              event.EndUTC.Format(DateFormat),
		DefiningTimezone: base.PtrValue(event.DefiningTimezone, "Australia/Sydney"),
		LastUpdatedUTC:   time.Time(event.LastUpdatedUTC),
		ScheduleType:     scheduleType,
	}
}
