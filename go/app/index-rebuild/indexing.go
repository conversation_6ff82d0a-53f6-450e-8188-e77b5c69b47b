package index_rebuild

import (
	jsonitor "github.com/json-iterator/go"

	"stash.abc-dev.net.au/rmp/rmp-program-api/indexing"
)

type IndexItem struct {
	Index string
	ID    string
	Data  any
}

func IndexFromChannel(bulk *indexing.BulkIndexer, statistics *Statistics, ch chan IndexItem) error {
	for item := range ch {
		_, err := bulk.Index(item.Index, item.ID, item.Data)
		if err != nil {
			return err
		}
		if v, ok := item.Data.(jsonitor.RawMessage); ok {
			size := int32(len(v))
			statistics.AddDocument(item.Index, size)
		}
	}
	bulk.Flush()
	return nil
}
