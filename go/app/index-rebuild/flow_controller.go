package index_rebuild

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"runtime"
	"sync"

	jsonitor "github.com/json-iterator/go"
	"github.com/opensearch-project/opensearch-go/v4/opensearchapi"
	"gorm.io/gorm"

	base "stash.abc-dev.net.au/rmp/rmp-program-api"
	"stash.abc-dev.net.au/rmp/rmp-program-api/indexing"
	"stash.abc-dev.net.au/rmp/rmp-program-api/models"
)

type FlowController struct {
	indexClient     *opensearchapi.Client
	dbFetcher       *DBFetcher
	convertor       *indexing.DBDataConvertor
	statistics      *Statistics
	programItemChan chan any
	fetchGroup      *sync.WaitGroup
	indexChan       chan IndexItem
}

func NewFlowController(dbFetcher *DBFetcher, indexingConfig base.IndexingConfiguration) *FlowController {
	// Connect to index engine and database
	indexClient, err := indexing.ConnectToSearch(indexingConfig)
	if err != nil {
		panic(err)
	}
	return &FlowController{
		indexClient:     indexClient,
		dbFetcher:       dbFetcher,
		convertor:       indexing.NewDBDataConvertor(dbFetcher.DB()),
		statistics:      NewStatistics(),
		programItemChan: make(chan any),
		fetchGroup:      &sync.WaitGroup{},
		indexChan:       make(chan IndexItem, 100000),
	}
}

func (fc *FlowController) Precache() error {
	return fc.convertor.Precache()
}

func (fc *FlowController) Exec(db *gorm.DB) error {
	episodes := base.NewSet[int]()
	fc.prepareDBItemFetchWorkers()
	wg := fc.collectIndex()
	rows, err := db.Rows()
	if err != nil {
		return err
	}
	defer rows.Close()
	for rows.Next() {
		var episode models.Episode
		err = db.ScanRows(rows, &episode)
		if err != nil {
			return err
		}
		if !episodes.Contains(episode.ID) {
			fc.fetchGroup.Add(1)
			episodes.Add(episode.ID)
			if episode.Segment != nil {
				episode.ChildSegments, err = fc.dbFetcher.GetChileSegments(&episode)
				if err != nil {
					return err
				}
			}
			fc.programItemChan <- &episode
		}
		if episode.Segment != nil {
			fc.fetchGroup.Add(1)
			segment := episode.Segment
			if episode.Series != nil {
				segment.ProgramID = episode.Series.PartOfProgram
			}
			segment.Episode = &episode
			fc.programItemChan <- segment
		}
	}
	close(fc.programItemChan)
	fc.fetchGroup.Wait()
	close(fc.indexChan)
	wg.Wait()
	fc.statistics.LogFinalStat()
	return nil
}

func (fc *FlowController) FetchEpisode(episode *models.Episode) error {
	defer fc.fetchGroup.Done()
	programItem, err := fc.convertor.FromDBEpisode(episode)
	if err != nil {
		return err
	}
	serialized, err := json.Marshal(programItem)
	if err != nil {
		return fmt.Errorf("failed to marshal program item: %w", err)
	}
	// fetch linked data
	fc.indexChan <- IndexItem{
		Index: indexing.IndexProgramItem,
		ID:    episode.ARID,
		Data:  jsonitor.RawMessage(serialized),
	}
	return nil
}

func (fc *FlowController) FetchSegment(segment *models.Segment) error {
	defer fc.fetchGroup.Done()
	programItem, err := fc.convertor.FromDBSegment(segment)
	if err != nil {
		return err
	}
	serialized, err := json.Marshal(programItem)
	if err != nil {
		return fmt.Errorf("failed to marshal program item: %w", err)
	}
	// fetch linked data
	fc.indexChan <- IndexItem{
		Index: indexing.IndexProgramItem,
		ID:    segment.ARID,
		Data:  jsonitor.RawMessage(serialized),
	}
	return nil
}

func (fc *FlowController) RebuildMapping(rebuild bool) {
	if !rebuild {
		return
	}
	mappingPath := base.StringEnvOrDefault("MAPPING_PATH", "/app/mappings/opensearch")
	err := indexing.RebuildMapping(fc.indexClient, mappingPath, indexing.IndexProgramItem)
	if err != nil {
		panic(err)
	}
}

func (fc *FlowController) collectIndex() *sync.WaitGroup {
	bulk := indexing.NewBulkIndexer(fc.indexClient)
	wgIndex := &sync.WaitGroup{}
	wgIndex.Add(1)
	go func() {
		err := IndexFromChannel(bulk, fc.statistics, fc.indexChan)
		if err != nil {
			panic(err)
		}
		fc.indexClient.Indices.Forcemerge(context.Background(), &opensearchapi.IndicesForcemergeReq{
			Indices: []string{indexing.IndexProgramItem},
		})
		wgIndex.Done()
	}()
	return wgIndex
}

func (fc *FlowController) prepareDBItemFetchWorkers() {
	threadLimit := runtime.NumCPU() * 2
	slog.Info("prepare db fetch", "threads", threadLimit)
	for i := 0; i < threadLimit; i++ {
		go func() {
			for data := range fc.programItemChan {
				fc.statistics.CountFetchedData(data)
				switch value := data.(type) {
				case *models.Episode:
					err := fc.FetchEpisode(value)
					if err != nil {
						panic(err)
					}
				case *models.Segment:
					err := fc.FetchSegment(value)
					if err != nil {
						panic(err)
					}
				default:
					panic(fmt.Sprintf("unknown type: %T", value))
				}
			}
		}()
	}
}
