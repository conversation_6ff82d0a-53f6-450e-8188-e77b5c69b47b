package index_rebuild

import (
	"fmt"
	"log/slog"
	"os"
	"time"

	slogorm "github.com/orandin/slog-gorm"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	"stash.abc-dev.net.au/rmp/rmp-program-api/models"
)

type PlayFilter struct {
	ARID     string
	From     time.Time
	To       time.Time
	Program  string
	LiveOnly bool
}

type MappingSettings struct {
	Rebuild bool
	Path    string
}

type DBFetcher struct {
	db *gorm.DB
}

func NewDBFetcher(connstring string) *DBFetcher {
	glogger := slogorm.New(
		slogorm.WithHandler(slog.NewJSONHandler(os.Stdout, nil)),
		slogorm.WithSlowThreshold(time.Second),
		slogorm.SetLogLevel(slogorm.ErrorLogType, slog.LevelError),
		slogorm.SetLogLevel(slogorm.SlowQueryLogType, slog.LevelWarn),
		slogorm.SetLogLevel(slogorm.DefaultLogType, slog.LevelInfo),
	)
	db, err := gorm.Open(mysql.Open(connstring), &gorm.Config{
		PrepareStmt:            true,
		SkipDefaultTransaction: true,
		DisableAutomaticPing:   true,
		Logger:                 glogger,
	})
	if err != nil {
		panic(fmt.Sprintf("failed to connect database %s: %s", connstring, err.Error()))
	}
	sqlDB, err := db.DB()
	if err != nil {
		panic(err)
	}

	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	return &DBFetcher{
		db: db,
	}
}

func (d *DBFetcher) DB() *gorm.DB {
	return d.db
}

func (d *DBFetcher) GetChileSegments(episode *models.Episode) ([]*models.Segment, error) {
	segments := []*models.Segment{}
	result := d.db.Model(&models.Segment{}).
		Joins("PrimaryWebpage").
		Where("segment.partof_episode = ?", episode.ID).Find(&segments)
	if result.Error != nil {
		return nil, result.Error
	}
	return segments, nil
}

func (d *DBFetcher) FilterEpisodeByProgram(programARID string) (*gorm.DB, error) {
	db := d.db.Model(&models.Episode{}).
		Joins("PrimaryWebpage").
		Joins("Segment").
		Joins("Segment.PrimaryWebpage").
		InnerJoins("Series").
		InnerJoins("INNER JOIN program p ON Series.partof_program=p.id").
		Where("p.arid = ?", programARID)
	return db, nil
}

func (d *DBFetcher) FilterEpisodeByARID(arid string) (*gorm.DB, error) {
	db := d.db.Model(&models.Episode{}).
		Joins("PrimaryWebpage").
		Joins("Series").
		Joins("Segment").
		Joins("Segment.PrimaryWebpage").
		Where("episode.arid = ?", arid)
	return db, nil
}

func (d *DBFetcher) FilterEpisodeByRange(from, to string) (*gorm.DB, error) {
	db := d.db.Model(&models.Episode{}).
		Joins("PrimaryWebpage").
		Joins("Series").
		Joins("Segment").
		Joins("Segment.PrimaryWebpage")
	if from != "" && to != "" {
		db.Where("episode.last_updated_utc >= ? AND episode.last_updated_utc <= ?", from, to)
	} else if from != "" {
		db.Where("episode.last_updated_utc >= ?", from)
	} else if to != "" {
		db.Where("episode.last_updated_utc <= ?", to)
	}
	return db, nil
}
