package index_rebuild

import (
	"context"
	"log/slog"
	"strconv"
	"sync/atomic"
	"time"

	base "stash.abc-dev.net.au/rmp/rmp-program-api"
	"stash.abc-dev.net.au/rmp/rmp-program-api/indexing"
	"stash.abc-dev.net.au/rmp/rmp-program-api/models"
)

const (
	LogProgressCount = 100000
)

type Statistics struct {
	begin               time.Time
	Max                 atomic.Int32
	Buckets             []atomic.Int32
	FetchedEpisodes     atomic.Int32
	FetchedSegments     atomic.Int32
	IndexedProgramItems atomic.Int32
	IndexedLive         atomic.Int32
	memStat             *base.MemStat
}

func NewStatistics() *Statistics {
	s := &Statistics{
		begin:               time.Now(),
		Max:                 atomic.Int32{},
		Buckets:             make([]atomic.Int32, 0),
		FetchedEpisodes:     atomic.Int32{},
		FetchedSegments:     atomic.Int32{},
		IndexedProgramItems: atomic.Int32{},
		IndexedLive:         atomic.Int32{},
	}
	// start memory stat monitor
	memStat := &base.MemStat{}
	go func() {
		for range time.Tick(5 * time.Second) {
			memStat.RecordMemUsage()
		}
	}()
	s.memStat = memStat
	for i := 0; i < 101; i++ { // every 10kb a bucket plus one for under 1kb
		s.Buckets = append(s.Buckets, atomic.Int32{})
	}
	return s
}

func (s *Statistics) CountFetchedData(data any) {
	switch data.(type) {
	case *models.Episode:
		newValue := s.FetchedEpisodes.Add(1)
		if newValue%LogProgressCount == 0 {
			slog.LogAttrs(context.Background(), slog.LevelInfo, "progress",
				slog.String("duration", time.Since(s.begin).String()),
				s.logFetched(),
				s.logMemory(),
			)
		}
	case *models.Segment:
		newValue := s.FetchedSegments.Add(1)
		if newValue%LogProgressCount == 0 {
			slog.LogAttrs(context.Background(), slog.LevelInfo, "progress",
				slog.String("duration", time.Since(s.begin).String()),
				s.logFetched(),
				s.logMemory(),
			)
		}
	}
}

func (s *Statistics) AddDocument(index string, size int32) {
	switch index {
	case indexing.IndexProgramItem:
		s.IndexedProgramItems.Add(1)
	case indexing.IndexLive:
		s.IndexedLive.Add(1)
	}
	if size > s.Max.Load() {
		s.Max.Store(size)
	}
	if size < 1000 {
		s.Buckets[0].Add(1)
		return
	}
	bucket := size/(10000) + 1
	if bucket > 100 {
		bucket = 100
	}
	s.Buckets[bucket].Add(1)
}

func (s *Statistics) logFetched() slog.Attr {
	attrs := make([]any, 0, 2)
	attrs = append(attrs, slog.Int("episodes", int(s.FetchedEpisodes.Load())))
	attrs = append(attrs, slog.Int("segments", int(s.FetchedSegments.Load())))
	return slog.Group("fetched", attrs...)
}

func (s *Statistics) logMemory() slog.Attr {
	return slog.Group("memory",
		slog.Float64("num_goroutines", s.memStat.NumGoroutines()),
		slog.Float64("heap_sys_mb", s.memStat.HeapSys()),
		slog.Float64("heap_idle_mb", s.memStat.HeapIdle()),
		slog.Float64("heap_alloc_mb", s.memStat.HeapAlloc()),
	)
}

func (s *Statistics) logDocumentSizes() slog.Attr {
	attrs := make([]any, 0, 102)
	attrs = append(attrs, slog.Int("max", int(s.Max.Load())))
	attrs = append(attrs, slog.Int("0 KB ~ 1 KB", int(s.Buckets[0].Load())))
	begin := "1 KB"
	for i := 1; i <= 100; i++ {
		end := strconv.Itoa(i*10) + " KB"
		if v := s.Buckets[i].Load(); v > 0 {
			attrs = append(attrs, slog.Int(begin+" ~ "+end, int(v)))
		}
		begin = end
	}
	return slog.Group("document_size", attrs...)

}

func (s *Statistics) LogFinalStat() {
	slog.LogAttrs(context.Background(), slog.LevelInfo, "statistics",
		slog.String("duration", time.Since(s.begin).String()),
		s.logFetched(),
		slog.Group("indexed",
			slog.Int("program_items", int(s.IndexedProgramItems.Load())),
			slog.Int("lives", int(s.IndexedLive.Load())),
		),
		s.logDocumentSizes(),
		s.logMemory(),
	)
}
