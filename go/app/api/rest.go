package api

import (
	"bytes"
	"context"
	"fmt"
	"log/slog"
	"net/http"

	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
)

func NewRestRouter(h *Handler) *echo.Echo {
	e := echo.New()
	e.Use(middleware.Logger())
	e.Use(middleware.Recover())
	e.Use(middleware.CORS())

	e.GET("/status", status)
	g := e.Group("/api/v1")
	g.GET("/services", services(h))
	g.GET("/programitems", programItems(h))
	g.GET("/listqueues", func(c echo.Context) error {
		queues, err := listQueues()

		if err != nil {
			println(fmt.Sprintf("%s", err))
		}

		var queueBuffer bytes.Buffer

		for i, url := range queues {
			queueBuffer.WriteString(fmt.Sprintf("%v: %v\n", i, url))
		}

		return c.String(http.StatusOK, fmt.Sprintf("%v", queueBuffer.String()))
	})

	return e
}

func status(c echo.Context) error {
	return c.NoContent(http.StatusOK)
}

func services(h *Handler) echo.HandlerFunc {
	return func(c echo.Context) error {
		services, err := h.Services()
		if err != nil {
			return c.JSON(http.StatusInternalServerError, err)
		}
		return c.JSON(http.StatusOK, services)
	}
}

func programItems(h *Handler) echo.HandlerFunc {
	return func(c echo.Context) error {
		items, err := h.ProgramItems()
		if err != nil {
			return c.JSON(http.StatusInternalServerError, err)
		}
		return c.JSON(http.StatusOK, items)
	}
}

func listQueues() ([]string, error) {
	ctx := context.Background()
	sdkConfig, err := config.LoadDefaultConfig(ctx)
	if err != nil {
		fmt.Println("Couldn't load default configuration. Have you set up your AWS account?")
		fmt.Println(err)
		return nil, err
	}
	sqsClient := sqs.NewFromConfig(sdkConfig)

	fmt.Println("Let's list the queues for your account.")
	var queueUrls []string
	paginator := sqs.NewListQueuesPaginator(sqsClient, &sqs.ListQueuesInput{})
	for paginator.HasMorePages() {
		output, err := paginator.NextPage(ctx)
		if err != nil {
			slog.Error("Couldn't get queues. Here's why", "error", err.Error())
			break
		} else {
			queueUrls = append(queueUrls, output.QueueUrls...)
		}
	}
	if len(queueUrls) == 0 {
		slog.Info("You don't have any queues!")
	} else {
		for _, queueUrl := range queueUrls {
			slog.Info("queue", "queueUrl", queueUrl)
		}
	}
	return queueUrls, nil
}
