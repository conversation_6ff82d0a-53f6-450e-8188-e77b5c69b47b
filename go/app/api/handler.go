package api

import (
	"context"

	jsonitor "github.com/json-iterator/go"
	"github.com/opensearch-project/opensearch-go/v4/opensearchapi"
	"gorm.io/gorm"

	"stash.abc-dev.net.au/rmp/rmp-program-api/indexing"
	"stash.abc-dev.net.au/rmp/rmp-program-api/models"
)

var json = jsonitor.ConfigCompatibleWithStandardLibrary

type Handler struct {
	db   *gorm.DB
	srch *opensearchapi.Client
}

func NewHandler(db *gorm.DB, srch *opensearchapi.Client) *Handler {
	return &Handler{db: db, srch: srch}
}

func (h *Handler) Services() ([]models.Service, error) {
	var services []models.Service
	if err := h.db.Find(&services).Error; err != nil {
		return nil, err
	}
	return services, nil
}

func (h *Handler) ProgramItems() ([]*indexing.ProgramItem, error) {
	res, err := h.srch.Search(context.Background(), &opensearchapi.SearchReq{
		Indices: []string{indexing.IndexProgramItem, indexing.IndexLive},
	})
	if err != nil {
		return nil, err
	}
	result := make([]*indexing.ProgramItem, 0, len(res.Hits.Hits))
	for _, hit := range res.Hits.Hits {
		item := &indexing.ProgramItem{}
		if err := json.Unmarshal(hit.Source, item); err != nil {
			return nil, err
		}
		result = append(result, item)
	}
	return result, nil
}
