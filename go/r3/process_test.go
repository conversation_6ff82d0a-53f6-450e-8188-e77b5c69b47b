package r3

import (
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

func TestCalculateDuration(t *testing.T) {
	tests := []struct {
		name      string
		startTime string
		endTime   string
		expected  int
	}{
		{
			name:      "one hour duration",
			startTime: "2023-01-01T10:00:00Z",
			endTime:   "2023-01-01T11:00:00Z",
			expected:  3600,
		},
		{
			name:      "30 minutes duration",
			startTime: "2023-01-01T10:00:00Z",
			endTime:   "2023-01-01T10:30:00Z",
			expected:  1800,
		},
		{
			name:      "invalid start time",
			startTime: "invalid",
			endTime:   "2023-01-01T11:00:00Z",
			expected:  0,
		},
		{
			name:      "invalid end time",
			startTime: "2023-01-01T10:00:00Z",
			endTime:   "invalid",
			expected:  0,
		},
		{
			name:      "zero duration",
			startTime: "2023-01-01T10:00:00Z",
			endTime:   "2023-01-01T10:00:00Z",
			expected:  0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := calculateDuration(tt.startTime, tt.endTime)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestProcessSchedule(t *testing.T) {
	// Save original variables and restore after test
	originalProcessed := processed
	originalSkipped := skipped
	originalFailed := failed

	defer func() {
		processed = originalProcessed
		skipped = originalSkipped
		failed = originalFailed
	}()

	originalDB := db
	originalSQSClient := sqsClient
	defer func() {
		db = originalDB
		sqsClient = originalSQSClient
	}()

	// Create a new mock database
	mockDB, sqlMock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("Failed to create mock database: %v", err)
	}
	defer mockDB.Close()

	// Create a gorm DB instance with the mock
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      mockDB,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{})
	if err != nil {
		t.Fatalf("Failed to create gorm DB: %v", err)
	}
	db = gormDB

	// Create mock SQS client
	mockSQSClient := new(MockSQSClient)
	sqsClient = mockSQSClient

	tests := []struct {
		name        string
		schedule    Schedule
		expectError bool
		setupMocks  func(sqlMock sqlmock.Sqlmock, sqsMock *MockSQSClient)
	}{
		{
			name: "nil service",
			schedule: Schedule{
				Service: nil,
			},
			expectError: true,
			setupMocks: func(mock sqlmock.Sqlmock, sqsMock *MockSQSClient) {
				// Reset counters
				processed = make(map[string]int)
				skipped = make(map[string]int)
				failed = make(map[string]int)
			},
		},
		{
			name: "valid schedule with no programs",
			schedule: Schedule{
				Service: &Service{
					ServiceID: "test-service",
				},
				Programs: &Programs{
					Program: []*Program{},
				},
			},

			expectError: false,
			setupMocks: func(sqlMock sqlmock.Sqlmock, sqsMock *MockSQSClient) {
				// Reset counters
				processed = make(map[string]int)
				skipped = make(map[string]int)
				failed = make(map[string]int)

				sqlMock.ExpectQuery("SELECT").
					WithArgs("test-service", 1).
					WillReturnRows(sqlmock.NewRows([]string{"hash", "arid"}).
						AddRow("test-hash", "test-arid"))

				sqsMock.On("SendMessage", mock.Anything, mock.AnythingOfType("*sqs.SendMessageInput")).Return(
					&sqs.SendMessageOutput{MessageId: aws.String("test-message-id")},
					nil,
				).Once()
			},
		},
		{
			name: "valid schedule with program but no series",
			schedule: Schedule{
				Service: &Service{
					ServiceID: "test-service",
				},
				Programs: &Programs{
					Program: []*Program{
						{
							RapidID: "test-program",
							Serial:  []*Series{},
						},
					},
				},
			},
			expectError: false,
			setupMocks: func(sqlMock sqlmock.Sqlmock, sqsMock *MockSQSClient) {
				// Reset counters
				processed = make(map[string]int)
				skipped = make(map[string]int)
				failed = make(map[string]int)

				sqlMock.ExpectQuery("SELECT").
					WithArgs("test-service", 1).
					WillReturnRows(sqlmock.NewRows([]string{"hash", "arid"}).
						AddRow("test-hash", "test-arid"))

				sqlMock.ExpectQuery("SELECT").
					WithArgs("test-program", 1).
					WillReturnRows(sqlmock.NewRows([]string{"hash", "arid"}).
						AddRow("test-hash", "test-arid"))

				sqsMock.On("SendMessage", mock.Anything, mock.AnythingOfType("*sqs.SendMessageInput")).Return(
					&sqs.SendMessageOutput{MessageId: aws.String("test-message-id")},
					nil,
				).Twice()
			},
		},
		{
			name: "valid schedule with program and episodes without series",
			schedule: Schedule{
				Service: &Service{
					ServiceID: "test-service",
				},
				Programs: &Programs{
					Program: []*Program{
						{
							RapidID: "test-program",
							Serial:  []*Series{},
							Episodes: []*Episode{
								{
									RapidID: "test-episode",
								},
							},
						},
					},
				},
			},
			expectError: false,
			setupMocks: func(sqlMock sqlmock.Sqlmock, sqsMock *MockSQSClient) {
				// Reset counters
				processed = make(map[string]int)
				skipped = make(map[string]int)
				failed = make(map[string]int)

				sqlMock.ExpectQuery("SELECT").
					WithArgs("test-service", 1).
					WillReturnRows(sqlmock.NewRows([]string{"hash", "arid"}).
						AddRow("test-hash", "test-arid"))

				sqlMock.ExpectQuery("SELECT").
					WithArgs("test-program", 1).
					WillReturnRows(sqlmock.NewRows([]string{"hash", "arid"}).
						AddRow("test-hash", "test-arid"))

				sqsMock.On("SendMessage", mock.Anything, mock.AnythingOfType("*sqs.SendMessageInput")).Return(
					&sqs.SendMessageOutput{MessageId: aws.String("test-message-id")},
					nil,
				).Times(3)
			},
		},
		{
			name: "valid schedule with program, series and episodes",
			schedule: Schedule{
				Service: &Service{
					ServiceID: "test-service",
				},
				Programs: &Programs{
					Program: []*Program{
						{
							RapidID: "test-program",
							Serial: []*Series{
								{
									RapidID: "test-series",
									Episodes: []*Episode{
										{
											RapidID: "test-episode",
											PublicationEvents: []*PublicationEvent{
												{
													RapidID:   "test-pubevent",
													Type:      "Live",
													StartTime: "2023-01-01T10:00:00Z",
													EndTime:   "2023-01-01T11:00:00Z",
												},
											},
										},
									},
								},
							},
						},
					},
				},
			},
			expectError: false,
			setupMocks: func(sqlmock sqlmock.Sqlmock, sqsMock *MockSQSClient) {
				// Reset counters
				processed = make(map[string]int)
				skipped = make(map[string]int)
				failed = make(map[string]int)

				sqlMock.ExpectQuery("SELECT").
					WithArgs("test-service", 1).
					WillReturnRows(sqlmock.NewRows([]string{"hash", "arid"}).
						AddRow("test-hash", "test-arid"))

				sqlMock.ExpectQuery("SELECT").
					WithArgs("test-program", 1).
					WillReturnRows(sqlmock.NewRows([]string{"hash", "arid"}).
						AddRow("test-hash", "test-arid"))

				sqlMock.ExpectQuery("SELECT").
					WithArgs("test-series", 1).
					WillReturnRows(sqlmock.NewRows([]string{"hash", "arid"}).
						AddRow("test-hash", "test-arid"))

				sqlMock.ExpectQuery("SELECT").
					WithArgs("test-pubevent", 1).
					WillReturnRows(sqlmock.NewRows([]string{"hash", "arid"}).
						AddRow("test-hash", "test-arid"))

				sqlMock.ExpectQuery("SELECT").
					WithArgs("test-episode_1", 1).
					WillReturnRows(sqlmock.NewRows([]string{"hash", "arid"}).
						AddRow("test-hash", "test-arid"))

				sqlMock.ExpectQuery("SELECT").
					WithArgs("test-episode", 1).
					WillReturnRows(sqlmock.NewRows([]string{"hash", "arid"}).
						AddRow("test-hash", "test-arid"))

				sqsMock.On("SendMessage", mock.Anything, mock.AnythingOfType("*sqs.SendMessageInput")).Return(
					&sqs.SendMessageOutput{MessageId: aws.String("test-message-id")},
					nil,
				).Times(6)
			},
		},
		{
			name: "valid schedule with global episode",
			schedule: Schedule{
				Service: &Service{
					ServiceID: "test-service",
				},
				Programs: &Programs{
					Program: []*Program{
						{
							RapidID: "test-program",
							Serial: []*Series{
								{
									RapidID: "test-series",
									Episodes: []*Episode{
										{
											RapidID:       "test-episode",
											GlobalEpisode: true,
											LinkID:        stringPtr("link-id"),
											PublicationEvents: []*PublicationEvent{
												{
													RapidID:   "test-pubevent",
													Type:      "Live",
													StartTime: "2023-01-01T10:00:00Z",
													EndTime:   "2023-01-01T11:00:00Z",
												},
											},
										},
									},
								},
							},
						},
					},
				},
			},
			expectError: false,
			setupMocks: func(sqlMock sqlmock.Sqlmock, sqsMock *MockSQSClient) {
				// Reset counters
				processed = make(map[string]int)
				skipped = make(map[string]int)
				failed = make(map[string]int)

				sqlMock.ExpectQuery("SELECT").
					WithArgs("test-service", 1).
					WillReturnRows(sqlmock.NewRows([]string{"hash", "arid"}).
						AddRow("test-hash", "test-arid"))

				sqlMock.ExpectQuery("SELECT").
					WithArgs("test-program", 1).
					WillReturnRows(sqlmock.NewRows([]string{"hash", "arid"}).
						AddRow("test-hash", "test-arid"))

				sqlMock.ExpectQuery("SELECT").
					WithArgs("test-series", 1).
					WillReturnRows(sqlmock.NewRows([]string{"hash", "arid"}).
						AddRow("test-hash", "test-arid"))

				sqlMock.ExpectQuery("SELECT").
					WithArgs("test-pubevent", 1).
					WillReturnRows(sqlmock.NewRows([]string{"hash", "arid"}).
						AddRow("test-hash", "test-arid"))

					// version
				sqlMock.ExpectQuery("SELECT").
					WithArgs("test-program_test-series_link-id_1", 1).
					WillReturnRows(sqlmock.NewRows([]string{"hash", "arid"}).
						AddRow("test-hash", "test-arid"))

					// episode
				sqlMock.ExpectQuery("SELECT").
					WithArgs("test-program_test-series_link-id", 1).
					WillReturnRows(sqlmock.NewRows([]string{"hash", "arid"}).
						AddRow("test-hash", "test-arid"))

				sqsMock.On("SendMessage", mock.Anything, mock.AnythingOfType("*sqs.SendMessageInput")).Return(
					&sqs.SendMessageOutput{MessageId: aws.String("test-message-id")},
					nil,
				).Times(6)
			},
		},
		{
			name: "global episode without link ID",
			schedule: Schedule{
				Service: &Service{
					ServiceID: "test-service",
				},
				Programs: &Programs{
					Program: []*Program{
						{
							RapidID: "test-program",
							Serial: []*Series{
								{
									RapidID: "test-series",
									Episodes: []*Episode{
										{
											RapidID:       "test-episode",
											GlobalEpisode: true,
											LinkID:        nil,
											PublicationEvents: []*PublicationEvent{
												{
													RapidID:   "test-pubevent",
													Type:      "Live",
													StartTime: "2023-01-01T10:00:00Z",
													EndTime:   "2023-01-01T11:00:00Z",
												},
											},
										},
									},
								},
							},
						},
					},
				},
			},
			expectError: false, // We don't return the error from processEpisode
			setupMocks: func(sqlMock sqlmock.Sqlmock, sqsMock *MockSQSClient) {
				// Reset counters
				processed = make(map[string]int)
				skipped = make(map[string]int)
				failed = make(map[string]int)

				sqlMock.ExpectQuery("SELECT").
					WithArgs("test-service", 1).
					WillReturnRows(sqlmock.NewRows([]string{"hash", "arid"}).
						AddRow("test-hash", "test-arid"))

				sqlMock.ExpectQuery("SELECT").
					WithArgs("test-program", 1).
					WillReturnRows(sqlmock.NewRows([]string{"hash", "arid"}).
						AddRow("test-hash", "test-arid"))

				sqlMock.ExpectQuery("SELECT").
					WithArgs("test-series", 1).
					WillReturnRows(sqlmock.NewRows([]string{"hash", "arid"}).
						AddRow("test-hash", "test-arid"))

				sqsMock.On("SendMessage", mock.Anything, mock.AnythingOfType("*sqs.SendMessageInput")).Return(
					&sqs.SendMessageOutput{MessageId: aws.String("test-message-id")},
					nil,
				).Twice()
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mocks
			tt.setupMocks(sqlMock, mockSQSClient)

			// Call the function
			err := ProcessSchedule(tt.schedule)

			// Verify results
			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)

				// Verify service was processed
				if tt.schedule.Service != nil {
					assert.Equal(t, 1, processed[ServiceType])
				}

				// Verify programs were processed
				if tt.schedule.Programs.Program != nil {
					assert.Equal(t, len(tt.schedule.Programs.Program), processed[ProgramType])
				}

				// Verify series were processed
				seriesCount := 0
				for _, program := range tt.schedule.Programs.Program {
					seriesCount += len(program.Serial)
				}
				if seriesCount > 0 {
					assert.Equal(t, seriesCount, processed[SeriesType])
				}

				// Verify episodes were processed
				episodeCount := 0
				for _, program := range tt.schedule.Programs.Program {
					for _, series := range program.Serial {
						// skip episode if it is global episode and link id is nil
						if series.Episodes[0].GlobalEpisode && series.Episodes[0].LinkID == nil {
							continue
						}
						episodeCount += len(series.Episodes)
					}
				}
				if episodeCount > 0 {
					assert.Equal(t, episodeCount, processed[EpisodeType])
				}

				// Verify publication events were processed
				pubEventCount := 0
				for _, program := range tt.schedule.Programs.Program {
					for _, series := range program.Serial {
						for _, episode := range series.Episodes {
							// skip episode if it is global episode and link id is nil
							if series.Episodes[0].GlobalEpisode && series.Episodes[0].LinkID == nil {
								continue
							}
							pubEventCount += len(episode.PublicationEvents)
						}
					}
				}
				if pubEventCount > 0 {
					assert.Equal(t, pubEventCount, processed[PublicationEventType])
				}

				// Verify versions were created for Live publication events
				liveCount := 0
				for _, program := range tt.schedule.Programs.Program {
					for _, series := range program.Serial {
						for _, episode := range series.Episodes {
							// skip episode if it is global episode and link id is nil
							if series.Episodes[0].GlobalEpisode && series.Episodes[0].LinkID == nil {
								continue
							}
							for _, pubEvent := range episode.PublicationEvents {
								if pubEvent.Type == "Live" {
									liveCount++
								}
							}
						}
					}
				}
				if liveCount > 0 {
					assert.Equal(t, liveCount, processed[VersionType])
				}
			}
		})
	}
}

// Helper function to create string pointer
func stringPtr(s string) *string {
	return &s
}
