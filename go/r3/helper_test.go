package r3

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestCheckURL(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "empty url",
			input:    "",
			expected: "",
		},
		{
			name:     "http to https",
			input:    "http://example.com",
			expected: "https://example.com",
		},
		{
			name:     "mpegmedia to akamaized",
			input:    "https://mpegmedia.abc.net.au/news/audio/123.mp3",
			expected: "https://abcmedia.akamaized.net/news/audio/123.mp3",
		},
		{
			name:     "both transformations",
			input:    "http://mpegmedia.abc.net.au/news/audio/123.mp3",
			expected: "https://abcmedia.akamaized.net/news/audio/123.mp3",
		},
		{
			name:     "no transformation needed",
			input:    "https://example.com",
			expected: "https://example.com",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := checkURL(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestProcessWebPage(t *testing.T) {
	tests := []struct {
		name     string
		webPage  *WebPage
		program  *Program
		expected *WebPage
	}{
		{
			name:     "nil webpage",
			webPage:  nil,
			program:  &Program{},
			expected: nil,
		},
		{
			name: "empty url",
			webPage: &WebPage{
				URL: "",
			},
			program:  &Program{},
			expected: nil,
		},
		{
			name: "http only url",
			webPage: &WebPage{
				URL: "http://",
			},
			program:  &Program{},
			expected: nil,
		},
		{
			name: "https only url",
			webPage: &WebPage{
				URL: "https://",
			},
			program:  &Program{},
			expected: nil,
		},
		{
			name: "valid url",
			webPage: &WebPage{
				URL: "https://example.com",
			},
			program: &Program{},
			expected: &WebPage{
				URL: "https://example.com",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := processWebPage(tt.webPage, tt.program.RapidID)
			if tt.expected != nil {
				tt.expected.Process(tt.program.RapidID)
			}
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestValidURL(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected bool
	}{
		{
			name:     "valid https url",
			input:    "https://example.com",
			expected: true,
		},
		{
			name:     "valid http url",
			input:    "http://example.com",
			expected: true,
		},
		{
			name:     "valid url with path",
			input:    "https://example.com/path/to/resource",
			expected: true,
		},
		{
			name:     "valid url with query parameters",
			input:    "https://example.com/path?param=value&other=test",
			expected: true,
		},
		{
			name:     "valid url with fragment",
			input:    "https://example.com/path#fragment",
			expected: true,
		},
		{
			name:     "valid url with port",
			input:    "https://example.com:8080/path",
			expected: true,
		},
		{
			name:     "valid ftp url",
			input:    "ftp://example.com/file.txt",
			expected: true,
		},
		{
			name:     "valid rtmp url",
			input:    "rtmp://cp112896.live.edgefcs.net/live/dig_music",
			expected: true,
		},
		{
			name:     "valid rtmp url with @",
			input:    "rtmp://cp112896.live.edgefcs.net/live/dig_music@40680",
			expected: true,
		},
		{
			name:     "empty string",
			input:    "",
			expected: false,
		},
		{
			name:     "invalid url - no protocol",
			input:    "example.com",
			expected: false,
		},
		{
			name:     "invalid url - malformed",
			input:    "://example.com",
			expected: false,
		},
		{
			name:     "invalid url - spaces",
			input:    "https://example .com",
			expected: false,
		},
		{
			name:     "invalid url - only protocol",
			input:    "https://",
			expected: false,
		},
		{
			name:     "invalid url - only http",
			input:    "http://",
			expected: false,
		},
		{
			name:     "invalid url - random string",
			input:    "not a url at all",
			expected: false,
		},
		{
			name:     "invalid url - missing host",
			input:    "https:///path",
			expected: false,
		},
		{
			name:     "invalid url - null host",
			input:    "https:///null/mp3",
			expected: false,
		},
		{
			name:     "valid url - semicolon mp3",
			input:    "http://live-radio01.mediahubaustralia.com/6TJW/aac/;*.mp3",
			expected: true,
		},
		{
			name:     "valid url - semicolon",
			input:    "https://live-radio03.mediahubaustralia.com/2GOSW/aac/;",
			expected: true,
		},
		{
			name:     "valid url - space",
			input:    "https://live-radio03.mediahubaustralia.com/2GOSW/aac/ ",
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := validURL(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}
