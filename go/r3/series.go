package r3

import (
	"stash.abc-dev.net.au/rmp/rmp-program-api/models"
	"stash.abc-dev.net.au/rmp/rmp-program-api/pkg/arid"
)

const (
	SeriesType = "series"
)

// Series represents the <series> element inside <serial>
type Series struct {
	EntityType        string     `json:"type,omitempty"`
	ProgramRapidID    string     `json:"program,omitempty"`
	Title             string     `xml:"title" json:"title,omitempty"`
	ARID              string     `xml:"arid" json:"arid,omitempty"`
	RapidID           string     `xml:"rapid_series_id" json:"rapid_id,omitempty"`
	MiniDescription   string     `xml:"mini_description" json:"mini_description,omitempty"`
	ShortDescription  string     `xml:"short_description" json:"short_description,omitempty"`
	MediumDescription string     `xml:"medium_description" json:"medium_description,omitempty"`
	WebPage           *WebPage   `json:"webpage,omitempty"`
	Episodes          []*Episode `xml:"episodes>episode" json:"-"`
	Program           *Program   `json:"-"`
}

func (s *Series) Process(program *Program) {
	s.EntityType = SeriesType
	if len(s.ARID) == 0 {
		s.ARID = s.GenerateARID()
	}
	if program == nil {
		return
	}
	s.Program = program
	s.ProgramRapidID = program.RapidID
	s.WebPage = program.PrimaryWebPage
}

func (s *Series) GenerateARID() string {
	return arid.Generate(s.RapidID, models.SeriesConfig)
}

func (s *Series) GetType() string {
	return SeriesType
}

func (s *Series) GetARID() string {
	return s.ARID
}

func (s *Series) GetRapidID() string {
	return s.RapidID
}

func (s *Series) GetServiceID() string {
	return s.Program.GetServiceID()
}

func (s *Series) SetARID(arid string) {
	s.ARID = arid
}
