package r3

import (
	"fmt"
	"strconv"
	"time"

	"stash.abc-dev.net.au/rmp/rmp-program-api/models"
)

var (
	processed = make(map[string]int)
	skipped   = make(map[string]int)
	failed    = make(map[string]int)
)

func ProcessSchedule(schedule Schedule) error {
	if schedule.Service == nil {
		return fmt.Errorf("service cannot be nil")
	}

	// Initialize global logger with serviceID context
	InitLogger(schedule.Service.ServiceID)
	logger := Logger()
	logger.Debug("Processing Service")

	err := processService(schedule.Service)
	if err != nil {
		logger.Error("error processing service", "error", err.Error())
		// return err
	}

	// process programs
	for _, program := range schedule.Programs.Program {
		logger.Debug("Processing Program", "rapidID", program.RapidID, "program", program)
		err := processProgram(program, schedule.Service)
		if err != nil {
			logger.Error("error processing program", "error", err.Error())
		}

		// process series
		// log error if series is nil
		if len(program.Serial) == 0 {
			logger.Warn("program has no series", "rapidID", program.RapidID, "program", program)
		}

		// log error if episodes are under the program
		if len(program.Episodes) > 0 {
			// add more details for splunk alert
			logger.Warn("episodes attached without series to program", "rapidID", program.RapidID, "program", program)
		}

		for _, series := range program.Serial {
			logger.Debug("Processing Series", "rapidID", series.RapidID)
			err = processSeries(series, program)
			if err != nil {
				logger.Error("error processing series", "error", err.Error())
			}

			// process episodes
			for _, episode := range series.Episodes {
				logger.Debug("Processing Episode", "rapidID", episode.RapidID)
				err = processEpisode(episode, series)
				if err != nil {
					logger.Error("error processing episode", "error", err.Error())
				}
			}
		}

	}

	// print summary
	Logger().Info("Summary of processed entities for service "+schedule.Service.ServiceID, "processed", processed, "skipped", skipped, "failed", failed)
	return nil

}

func processService(service *Service) error {
	service.Process()
	return message(service)
}

func processProgram(program *Program, service *Service) error {
	program.Process(service)
	return message(program)
}

func processSeries(series *Series, program *Program) error {
	series.Process(program)
	return message(series)
}

func processEpisode(episode *Episode, series *Series) error {
	episode.Process(series)
	// determine rapid id for global episode
	if episode.GlobalEpisode {
		if episode.LinkID == nil {
			Logger().Warn("LinkID missing for global episode", "rapidID", episode.RapidID, "program", episode.ProgramRapidID, "series", episode.SeriesRapidID, "title", episode.Title)
			return fmt.Errorf("episode %s of program %s and series %s is a global episode but has no link id", episode.RapidID, episode.ProgramRapidID, episode.SeriesRapidID)
		}
		episode.RapidID = episode.ProgramRapidID + "_" + episode.SeriesRapidID + "_" + *episode.LinkID

		// regenerate arid by new rapidid
		// episode.ARID = episode.GenerateARID()
	}

	// episode
	if len(episode.ARID) == 0 {
		episode.ARID = episode.GenerateARID()
	}

	err := processPublicationEventsAndVersions(episode)
	if err != nil {
		Logger().Error("Error processing versions", "error", err.Error())
		// return err
	}

	return message(episode)
}

func processPubEvent(pubevent *PublicationEvent, episode *Episode) error {

	pubevent.Process(episode)
	pubevent_duration := calculateDuration(pubevent.StartTime, pubevent.EndTime)
	pubevent.Duration = pubevent_duration

	return message(pubevent)
}

func processPublicationEventsAndVersions(episode *Episode) error {
	var episodeDuration int

	if episode == nil {
		return fmt.Errorf("episode is nil")
	}

	logger := Logger().With("episodeID", episode.RapidID)

	// versions := make([]*Version, 0)
	for _, pubevent := range episode.PublicationEvents {

		logger.Debug("Processing Publication Event", "rapidID", pubevent.RapidID, "pubevent", pubevent.Type)
		err := processPubEvent(pubevent, episode)
		if err != nil {
			logger.Error("error processing publication event", "error", err.Error())
		}

		if pubevent.Type != "Live" {
			continue
		}

		// determine episode's duration using live publication event
		if episodeDuration > 0 {
			episodeDuration += calculateDuration(pubevent.StartTime, pubevent.EndTime)
		} else {
			episodeDuration = calculateDuration(pubevent.StartTime, pubevent.EndTime)
		}

		// determine version - versions don't exist in the XML, they are created based off the episode and the types of publication event
		versionAttrType := models.GetVersionType(pubevent.Type)

		logger.Debug("Processing Version", "rapidID", episode.RapidID+"_"+strconv.Itoa(versionAttrType))

		// where publication event type is live, we use the episode arid, this covers the scenario of multiple live publicationevents all using the same version
		versionARID := pubevent.ARID
		if pubevent.IsLive() {
			versionARID = episode.ARID
		}
		//replace the first two letters (pe or pu) with 'pv'
		versionARID = models.VersionConfig.Prefix + versionARID[2:]

		version := &Version{
			EntityType:      VersionType,
			RapidID:         episode.RapidID + "_" + strconv.Itoa(versionAttrType),
			EpisodeRapidID:  episode.RapidID,
			Title:           pubevent.Type + " version for " + episode.Title,
			ARID:            versionARID,
			DurationSeconds: episodeDuration,
			VersionType:     versionAttrType,
			ServiceID:       pubevent.GetServiceID(),
		}
		// versions = append(versions, version)

		err = message(version)
		if err != nil {
			logger.Error("Error sending message for version", "error", err.Error(), "rapidID", episode.RapidID+"_"+strconv.Itoa(versionAttrType))
		}
	}

	// TODO: TBC PHP is using first events duration, not the sum of all live events
	// set episode duration, duration is pointer
	episode.Duration = episodeDuration

	return nil
}

func calculateDuration(start string, end string) int {
	// calculates duration between PublicationEvent startTime and endTime

	startTime, err := time.Parse(time.RFC3339, start)
	if err != nil {
		Logger().Error("FAILED: calculateDuration", "startTime", start, "error", err.Error())
		return 0 // error parsing start time
	}

	endTime, err := time.Parse(time.RFC3339, end)

	if err != nil {
		Logger().Error("FAILED: calculateDuration", "endTime", end, "error", err.Error())
		return 0
	}

	duration := endTime.Sub(startTime)

	return int(duration.Seconds())
}
