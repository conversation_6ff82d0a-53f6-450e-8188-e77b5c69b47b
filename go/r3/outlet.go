package r3

import (
	"stash.abc-dev.net.au/rmp/rmp-program-api/models"
	"stash.abc-dev.net.au/rmp/rmp-program-api/pkg/arid"
)

// Outlet represents an <outlet> element with a specific type
type Outlet struct {
	ARID         string         `xml:"arid" json:"arid,omitempty"`
	Title        string         `xml:"title" json:"title,omitempty"`
	Type         string         `xml:"type,attr" json:"type,omitempty"`
	RapidID      string         `xml:"rapid_outlet_id" json:"rapid_id,omitempty"`
	AudioStreams []*AudioStream `xml:"audio_streams>audio_stream" json:"audiostreams,omitempty"`
}

func (o *Outlet) Process() {
	if len(o.ARID) == 0 {
		o.ARID = o.GenerateARID()
	}
	for _, audioStream := range o.AudioStreams {
		audioStream.Process()
	}
}

func (o *Outlet) GenerateARID() string {
	return arid.Generate(o.RapidID, models.OutletConfig)
}
