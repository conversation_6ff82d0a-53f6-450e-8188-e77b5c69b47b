package r3

import (
	"context"
	"errors"
	"io"
	"log/slog"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
	"github.com/aws/aws-sdk-go-v2/service/sqs/types"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

// Mock implementations
type MockR3 struct {
	mock.Mock
}

func (m *MockR3) GetType() string {
	args := m.Called()
	return args.String(0)
}

func (m *MockR3) GetARID() string {
	args := m.Called()
	return args.String(0)
}

func (m *MockR3) GetRapidID() string {
	args := m.Called()
	return args.String(0)
}

func (m *MockR3) GetServiceID() string {
	args := m.Called()
	return args.String(0)
}

func (m *MockR3) SetARID(arid string) {
	m.Called(arid)
}

// simplified json marshal
func (m *MockR3) MarshalJSON() ([]byte, error) {
	if m.GetRapidID() == "test-marshal-error" {
		return nil, errors.New("marshal error")
	}
	return []byte(`{}`), nil
}

type MockSQSClient struct {
	mock.Mock
}

func (m *MockSQSClient) SendMessage(ctx context.Context, params *sqs.SendMessageInput, optFns ...func(*sqs.Options)) (*sqs.SendMessageOutput, error) {
	args := m.Called(ctx, params)
	return args.Get(0).(*sqs.SendMessageOutput), args.Error(1)
}

// Tests
func TestCalculateHash(t *testing.T) {
	tests := []struct {
		name     string
		input    []byte
		expected string
		wantErr  bool
	}{
		{
			name:     "valid input",
			input:    []byte("test data"),
			expected: "eb733a00c0c9d336e65691a37ab54293",
			wantErr:  false,
		},
		{
			name:     "empty input",
			input:    []byte(""),
			expected: "d41d8cd98f00b204e9800998ecf8427e",
			wantErr:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := calculateHash(tt.input)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestSendMessage(t *testing.T) {
	// Save original client and restore after test
	originalClient := sqsClient
	defer func() { sqsClient = originalClient }()

	mockClient := new(MockSQSClient)
	sqsClient = mockClient

	input := &sqs.SendMessageInput{
		QueueUrl:    aws.String("test-queue"),
		MessageBody: aws.String("test-message"),
	}

	// Test successful message sending
	t.Run("successful send", func(t *testing.T) {
		mockClient.On("SendMessage", mock.Anything, input).Return(
			&sqs.SendMessageOutput{MessageId: aws.String("test-message-id")},
			nil,
		).Once()

		err := sendMessage(input)
		assert.NoError(t, err)
		mockClient.AssertExpectations(t)
	})

	// Test error case
	t.Run("send error", func(t *testing.T) {
		mockClient.On("SendMessage", mock.Anything, input).Return(
			&sqs.SendMessageOutput{MessageId: aws.String("")},
			errors.New("sqs error"),
		).Once()

		err := sendMessage(input)
		assert.Error(t, err)
		mockClient.AssertExpectations(t)
	})
}

func TestGetHashFromDB(t *testing.T) {
	slog.SetDefault(slog.New(slog.NewTextHandler(io.Discard, nil)))
	// Save original db and restore after test
	originalDB := db
	defer func() { db = originalDB }()

	// Create a new mock database
	mockDB, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("Failed to create mock database: %v", err)
	}
	defer mockDB.Close()

	// Create a gorm DB instance with the mock
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      mockDB,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{})
	if err != nil {
		t.Fatalf("Failed to create gorm DB: %v", err)
	}
	db = gormDB

	tests := []struct {
		name        string
		data        *MockR3
		setupMock   func(mock sqlmock.Sqlmock, data *MockR3)
		expected    *HashModel
		expectError bool
	}{
		{
			name: "program found",
			data: func() *MockR3 {
				m := new(MockR3)
				m.On("GetType").Return(ProgramType)
				m.On("GetRapidID").Return("test-rapid-id")
				m.On("GetServiceID").Return("test-service-id")
				return m
			}(),
			setupMock: func(mock sqlmock.Sqlmock, data *MockR3) {
				mock.ExpectQuery("SELECT").
					WithArgs("test-rapid-id", 1).
					WillReturnRows(sqlmock.NewRows([]string{"hash", "arid"}).
						AddRow("test-hash", "test-arid"))
			},
			expected: &HashModel{
				Hash: "test-hash",
				ARID: "test-arid",
			},
			expectError: false,
		},
		{
			name: "series found",
			data: func() *MockR3 {
				m := new(MockR3)
				m.On("GetType").Return(SeriesType)
				m.On("GetRapidID").Return("test-rapid-id")
				m.On("GetServiceID").Return("test-service-id")
				return m
			}(),
			setupMock: func(mock sqlmock.Sqlmock, data *MockR3) {
				mock.ExpectQuery("SELECT").
					WithArgs("test-rapid-id", 1).
					WillReturnRows(sqlmock.NewRows([]string{"hash", "arid"}).
						AddRow("test-hash", "test-arid"))
			},
			expected: &HashModel{
				Hash: "test-hash",
				ARID: "test-arid",
			},
			expectError: false,
		},
		{
			name: "episode found",
			data: func() *MockR3 {
				m := new(MockR3)
				m.On("GetType").Return(EpisodeType)
				m.On("GetRapidID").Return("test-rapid-id")
				m.On("GetServiceID").Return("test-service-id")
				return m
			}(),
			setupMock: func(mock sqlmock.Sqlmock, data *MockR3) {
				mock.ExpectQuery("SELECT").
					WithArgs("test-rapid-id", 1).
					WillReturnRows(sqlmock.NewRows([]string{"hash", "arid"}).
						AddRow("test-hash", "test-arid"))
			},
			expected: &HashModel{
				Hash: "test-hash",
				ARID: "test-arid",
			},
			expectError: false,
		},
		{
			name: "publication event found",
			data: func() *MockR3 {
				m := new(MockR3)
				m.On("GetType").Return(PublicationEventType)
				m.On("GetRapidID").Return("test-rapid-id")
				m.On("GetServiceID").Return("test-service-id")
				return m
			}(),
			setupMock: func(mock sqlmock.Sqlmock, data *MockR3) {
				mock.ExpectQuery("SELECT").
					WithArgs("test-rapid-id", 1).
					WillReturnRows(sqlmock.NewRows([]string{"hash", "arid"}).
						AddRow("test-hash", "test-arid"))
			},
			expected: &HashModel{
				Hash: "test-hash",
				ARID: "test-arid",
			},
			expectError: false,
		},
		{
			name: "version found",
			data: func() *MockR3 {
				m := new(MockR3)
				m.On("GetType").Return(VersionType)
				m.On("GetRapidID").Return("test-rapid-id")
				m.On("GetServiceID").Return("test-service-id")
				return m
			}(),
			setupMock: func(mock sqlmock.Sqlmock, data *MockR3) {
				mock.ExpectQuery("SELECT").
					WithArgs("test-rapid-id", 1).
					WillReturnRows(sqlmock.NewRows([]string{"hash", "arid"}).
						AddRow("test-hash", "test-arid"))
			},
			expected: &HashModel{
				Hash: "test-hash",
				ARID: "test-arid",
			},
			expectError: false,
		},
		{
			name: "program not found",
			data: func() *MockR3 {
				m := new(MockR3)
				m.On("GetType").Return(ProgramType)
				m.On("GetRapidID").Return("non-existent-id")
				m.On("GetServiceID").Return("test-service-id")
				return m
			}(),
			setupMock: func(mock sqlmock.Sqlmock, data *MockR3) {
				mock.ExpectQuery("SELECT").
					WithArgs("non-existent-id", 1).
					WillReturnRows(sqlmock.NewRows([]string{"hash", "arid"}))
			},
			expected:    nil,
			expectError: false,
		},
		{
			name: "service found",
			data: func() *MockR3 {
				m := new(MockR3)
				m.On("GetType").Return(ServiceType)
				m.On("GetRapidID").Return("test-rapid-id")
				m.On("GetServiceID").Return("test-service-id")
				return m
			}(),
			setupMock: func(mock sqlmock.Sqlmock, data *MockR3) {
				mock.ExpectQuery("SELECT").
					WithArgs("test-service-id", 1).
					WillReturnRows(sqlmock.NewRows([]string{"hash", "arid"}).
						AddRow("test-hash", "test-arid"))
			},
			expected: &HashModel{
				Hash: "test-hash",
				ARID: "test-arid",
			},
			expectError: false,
		},
		// {
		// 	name: "database error",
		// 	data: func() *MockR3 {
		// 		m := new(MockR3)
		// 		m.On("GetType").Return(ProgramType)
		// 		m.On("GetRapidID").Return("test-rapid-id")
		// 		m.On("GetServiceID").Return("test-service-id")
		// 		return m
		// 	}(),
		// 	setupMock: func(mock sqlmock.Sqlmock, data *MockR3) {
		// 		mock.ExpectQuery("SELECT").
		// 			WithArgs("test-rapid-id", 1).
		// 			WillReturnError(errors.New("database error"))
		// 	},
		// 	expected:    nil,
		// 	expectError: true,
		// },
		{
			name: "unknown entity type",
			data: func() *MockR3 {
				m := new(MockR3)
				m.On("GetType").Return("UnknownType")
				m.On("GetRapidID").Return("test-rapid-id")
				m.On("GetServiceID").Return("test-service-id")
				return m
			}(),
			setupMock: func(mock sqlmock.Sqlmock, data *MockR3) {
				// No expectations needed as it should return an error before DB query
			},
			expected:    nil,
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMock(mock, tt.data)

			result, err := getHashFromDB(tt.data)

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}

			// Verify all expectations were met
			if err := mock.ExpectationsWereMet(); err != nil {
				t.Errorf("Unfulfilled expectations: %s", err)
			}
		})
	}
}

func TestGetMessageInput(t *testing.T) {
	// Save original db and restore after test
	originalDB := db
	// Save the original hasher
	originalHasher := hasher
	defer func() {
		db = originalDB
		hasher = originalHasher
	}()

	// Create a new mock database
	mockDB, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("Failed to create mock database: %v", err)
	}
	defer mockDB.Close()

	// Create a gorm DB instance with the mock
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      mockDB,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{})
	if err != nil {
		t.Fatalf("Failed to create gorm DB: %v", err)
	}
	db = gormDB

	tests := []struct {
		name        string
		data        *MockR3
		setupMock   func(mock sqlmock.Sqlmock, data *MockR3)
		mockHash    string
		mockHashErr error
		expected    *sqs.SendMessageInput
		expectError bool
		expectNil   bool
	}{
		{
			name: "new entity - create message",
			data: func() *MockR3 {
				m := new(MockR3)
				m.On("GetType").Return(ProgramType)
				m.On("GetRapidID").Return("test-rapid-id")
				m.On("GetServiceID").Return("test-service-id")
				m.On("GetARID").Return("test-arid")
				return m
			}(),
			setupMock: func(mock sqlmock.Sqlmock, data *MockR3) {
				mock.ExpectQuery("SELECT").
					WithArgs("test-rapid-id", 1).
					WillReturnRows(sqlmock.NewRows([]string{"hash", "arid"}))
			},
			mockHash:    "new-hash",
			mockHashErr: nil,
			expected: &sqs.SendMessageInput{
				QueueUrl:    aws.String(QueueUrl),
				MessageBody: aws.String("{}"), // Simplified for test
				MessageAttributes: map[string]types.MessageAttributeValue{
					"HASH": {
						DataType:    aws.String("String"),
						StringValue: aws.String("new-hash"),
					},
					"TYPE": {
						DataType:    aws.String("String"),
						StringValue: aws.String(ProgramType),
					},
					"DATE": {
						DataType:    aws.String("String"),
						StringValue: aws.String(""), // Will be ignored in comparison
					},
					"SERVICE": {
						DataType:    aws.String("String"),
						StringValue: aws.String("test-service-id"),
					},
					"ARID": {
						DataType:    aws.String("String"),
						StringValue: aws.String("test-arid"),
					},
					"RAPID_ID": {
						DataType:    aws.String("String"),
						StringValue: aws.String("test-rapid-id"),
					},
				},
			},
			expectError: false,
			expectNil:   false,
		},
		{
			name: "existing entity - hash changed",
			data: func() *MockR3 {
				m := new(MockR3)
				m.On("GetType").Return(ProgramType)
				m.On("GetRapidID").Return("test-rapid-id")
				m.On("GetServiceID").Return("test-service-id")
				m.On("GetARID").Return("test-arid")
				return m
			}(),
			setupMock: func(mock sqlmock.Sqlmock, data *MockR3) {
				mock.ExpectQuery("SELECT").
					WithArgs("test-rapid-id", 1).
					WillReturnRows(sqlmock.NewRows([]string{"hash", "arid"}).
						AddRow("old-hash", "test-arid"))
			},
			mockHash:    "new-hash",
			mockHashErr: nil,
			expected: &sqs.SendMessageInput{
				QueueUrl:    aws.String(QueueUrl),
				MessageBody: aws.String("{}"), // Simplified for test
				MessageAttributes: map[string]types.MessageAttributeValue{
					"HASH": {
						DataType:    aws.String("String"),
						StringValue: aws.String("new-hash"),
					},
					"TYPE": {
						DataType:    aws.String("String"),
						StringValue: aws.String(ProgramType),
					},
					"DATE": {
						DataType:    aws.String("String"),
						StringValue: aws.String(""), // Will be ignored in comparison
					},
					"SERVICE": {
						DataType:    aws.String("String"),
						StringValue: aws.String("test-service-id"),
					},
					"ARID": {
						DataType:    aws.String("String"),
						StringValue: aws.String("test-arid"),
					},
					"RAPID_ID": {
						DataType:    aws.String("String"),
						StringValue: aws.String("test-rapid-id"),
					},
				},
			},
			expectError: false,
			expectNil:   false,
		},
		{
			name: "existing entity - hash unchanged",
			data: func() *MockR3 {
				m := new(MockR3)
				m.On("GetType").Return(ProgramType)
				m.On("GetRapidID").Return("test-rapid-id")
				// m.On("GetServiceID").Return("test-service-id")
				m.On("GetARID").Return("test-arid")
				return m
			}(),
			setupMock: func(mock sqlmock.Sqlmock, data *MockR3) {
				mock.ExpectQuery("SELECT").
					WithArgs("test-rapid-id", 1).
					WillReturnRows(sqlmock.NewRows([]string{"hash", "arid"}).
						AddRow("same-hash", "test-arid"))
			},
			mockHash:    "same-hash",
			mockHashErr: nil,
			expected:    nil,
			expectError: false,
			expectNil:   true,
		},
		{
			name: "existing entity - arid updated",
			data: func() *MockR3 {
				m := new(MockR3)
				m.On("GetType").Return(ProgramType)
				m.On("GetRapidID").Return("test-rapid-id")
				m.On("GetServiceID").Return("test-service-id")
				m.On("GetARID").Return("old-arid")
				m.On("SetARID", "db-arid").Once()
				return m
			}(),
			setupMock: func(mock sqlmock.Sqlmock, data *MockR3) {
				mock.ExpectQuery("SELECT").
					WithArgs("test-rapid-id", 1).
					WillReturnRows(sqlmock.NewRows([]string{"hash", "arid"}).
						AddRow("old-hash", "db-arid"))
			},
			mockHash:    "new-hash",
			mockHashErr: nil,
			expected: &sqs.SendMessageInput{
				QueueUrl:    aws.String(QueueUrl),
				MessageBody: aws.String("{}"), // Simplified for test
				MessageAttributes: map[string]types.MessageAttributeValue{
					"HASH": {
						DataType:    aws.String("String"),
						StringValue: aws.String("new-hash"),
					},
					"TYPE": {
						DataType:    aws.String("String"),
						StringValue: aws.String(ProgramType),
					},
					"DATE": {
						DataType:    aws.String("String"),
						StringValue: aws.String(""), // Will be ignored in comparison
					},
					"SERVICE": {
						DataType:    aws.String("String"),
						StringValue: aws.String("test-service-id"),
					},
					"ARID": {
						DataType:    aws.String("String"),
						StringValue: aws.String("old-arid"), // This will be the original value in the test
					},
					"RAPID_ID": {
						DataType:    aws.String("String"),
						StringValue: aws.String("test-rapid-id"),
					},
				},
			},
			expectError: false,
			expectNil:   false,
		},
		// {
		// 	name: "database error",
		// 	data: func() *MockR3 {
		// 		m := new(MockR3)
		// 		m.On("GetType").Return(ProgramType)
		// 		m.On("GetRapidID").Return("test-rapid-id")
		// 		// m.On("GetServiceID").Return("test-service-id")
		// 		// m.On("GetARID").Return("test-arid")
		// 		return m
		// 	}(),
		// 	setupMock: func(mock sqlmock.Sqlmock, data *MockR3) {
		// 		mock.ExpectQuery("SELECT").
		// 			WithArgs("test-rapid-id", 1).
		// 			WillReturnError(errors.New("database error"))
		// 	},
		// 	mockHash:    "",
		// 	mockHashErr: nil,
		// 	expected:    nil,
		// 	expectError: true,
		// 	expectNil:   false,
		// },
		{
			name: "hasher error",
			data: func() *MockR3 {
				m := new(MockR3)
				m.On("GetType").Return(ProgramType)
				m.On("GetRapidID").Return("test-rapid-id")
				// m.On("GetServiceID").Return("test-service-id")
				// m.On("GetARID").Return("test-arid")
				return m
			}(),
			setupMock: func(mock sqlmock.Sqlmock, data *MockR3) {
				mock.ExpectQuery("SELECT").
					WithArgs("test-rapid-id", 1).
					WillReturnRows(sqlmock.NewRows([]string{"hash", "arid"}))
			},
			mockHash:    "",
			mockHashErr: errors.New("hash error"),
			expected:    nil,
			expectError: true,
			expectNil:   false,
		},
		{
			name: "marshal error",
			data: func() *MockR3 {
				m := new(MockR3)
				m.On("GetType").Return(ProgramType)
				m.On("GetRapidID").Return("test-marshal-error")
				// m.On("GetServiceID").Return("test-service-id")
				m.On("GetARID").Return("test-arid")
				return m
			}(),
			setupMock: func(mock sqlmock.Sqlmock, data *MockR3) {
				mock.ExpectQuery("SELECT").
					WithArgs("test-marshal-error", 1).
					WillReturnRows(sqlmock.NewRows([]string{"hash", "arid"}).AddRow("old-hash", "test-arid"))
			},
			mockHash:    "",
			mockHashErr: nil,
			expected:    nil,
			expectError: true,
			expectNil:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMock(mock, tt.data)

			// Mock the hasher.Sum function
			hasher = Hasher{
				Sum: func(b []byte) (string, error) {
					return tt.mockHash, tt.mockHashErr
				},
			}

			// Override json.Marshal for this test to return a simple JSON object
			// originalMarshal := json.Marshal
			// json.Marshal = func(v interface{}) ([]byte, error) {
			// 	if tt.name == "json marshal error" {
			// 		return nil, errors.New("marshal error")
			// 	}
			// 	return []byte("{}"), nil
			// }
			// defer func() { json.Marshal = originalMarshal }()

			result, err := getMessageInput(tt.data)

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else if tt.expectNil {
				assert.NoError(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)

				// Compare fields except for DATE which will be dynamic
				assert.Equal(t, tt.expected.QueueUrl, result.QueueUrl)
				assert.Equal(t, tt.expected.MessageBody, result.MessageBody)

				for key, expectedAttr := range tt.expected.MessageAttributes {
					if key != "DATE" {
						assert.Equal(t, expectedAttr.DataType, result.MessageAttributes[key].DataType)
						assert.Equal(t, expectedAttr.StringValue, result.MessageAttributes[key].StringValue)
					} else {
						// Just verify DATE exists and has the right format
						assert.NotNil(t, result.MessageAttributes["DATE"])
						assert.NotNil(t, result.MessageAttributes["DATE"].StringValue)
					}
				}
			}

			// Verify all expectations were met
			if err := mock.ExpectationsWereMet(); err != nil {
				t.Errorf("Unfulfilled expectations: %s", err)
			}

			// Verify mock expectations
			tt.data.AssertExpectations(t)
		})
	}
}

func TestMessage(t *testing.T) {
	// Save original variables and restore after test
	originalDB := db
	originalHasher := hasher
	originalSQSClient := sqsClient
	originalProcessed := processed
	originalSkipped := skipped
	originalFailed := failed

	defer func() {
		db = originalDB
		hasher = originalHasher
		sqsClient = originalSQSClient
		processed = originalProcessed
		skipped = originalSkipped
		failed = originalFailed
	}()

	// Reset counters for each test
	processed = make(map[string]int)
	skipped = make(map[string]int)
	failed = make(map[string]int)

	// Create mock SQS client
	mockSQSClient := new(MockSQSClient)
	sqsClient = mockSQSClient

	// Create mock database
	mockDB, sqlMock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("Failed to create mock database: %v", err)
	}
	defer mockDB.Close()

	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      mockDB,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{})
	if err != nil {
		t.Fatalf("Failed to create gorm DB: %v", err)
	}
	db = gormDB

	tests := []struct {
		name            string
		data            *MockR3
		setupMocks      func(sqlMock sqlmock.Sqlmock, sqsMock *MockSQSClient, data *MockR3)
		mockHash        string
		expectError     bool
		expectSkipped   bool
		expectFailed    bool
		expectProcessed bool
	}{
		{
			name: "new entity - successfully processed",
			data: func() *MockR3 {
				m := new(MockR3)
				m.On("GetType").Return(ProgramType)
				m.On("GetRapidID").Return("test-rapid-id")
				m.On("GetServiceID").Return("test-service-id")
				m.On("GetARID").Return("test-arid")
				return m
			}(),
			setupMocks: func(sqlMock sqlmock.Sqlmock, sqsMock *MockSQSClient, data *MockR3) {
				// DB mock
				sqlMock.ExpectQuery("SELECT").
					WithArgs("test-rapid-id", 1).
					WillReturnRows(sqlmock.NewRows([]string{"hash", "arid"}))

				// SQS mock
				sqsMock.On("SendMessage", mock.Anything, mock.AnythingOfType("*sqs.SendMessageInput")).Return(
					&sqs.SendMessageOutput{MessageId: aws.String("test-message-id")},
					nil,
				).Once()
			},
			mockHash:        "new-hash",
			expectError:     false,
			expectSkipped:   false,
			expectFailed:    false,
			expectProcessed: true,
		},
		{
			name: "existing entity - hash unchanged - skipped",
			data: func() *MockR3 {
				m := new(MockR3)
				m.On("GetType").Return(ProgramType)
				m.On("GetRapidID").Return("test-rapid-id")
				// m.On("GetServiceID").Return("test-service-id")
				m.On("GetARID").Return("test-arid")
				return m
			}(),
			setupMocks: func(sqlMock sqlmock.Sqlmock, sqsMock *MockSQSClient, data *MockR3) {
				// DB mock - return same hash to trigger skip
				sqlMock.ExpectQuery("SELECT").
					WithArgs("test-rapid-id", 1).
					WillReturnRows(sqlmock.NewRows([]string{"hash", "arid"}).
						AddRow("same-hash", "test-arid"))

				// No SQS expectations since message should be skipped
			},
			mockHash:        "same-hash",
			expectError:     false,
			expectSkipped:   true,
			expectFailed:    false,
			expectProcessed: false,
		},
		{
			name: "getMessageInput error",
			data: func() *MockR3 {
				m := new(MockR3)
				m.On("GetType").Return(ProgramType)
				m.On("GetRapidID").Return("test-rapid-id")
				// m.On("GetServiceID").Return("test-service-id")
				// m.On("GetARID").Return("test-arid")
				return m
			}(),
			setupMocks: func(sqlMock sqlmock.Sqlmock, sqsMock *MockSQSClient, data *MockR3) {
				// DB mock - return error
				sqlMock.ExpectQuery("SELECT").
					WithArgs("test-rapid-id", 1).
					WillReturnError(errors.New("database error"))

				// No SQS expectations since there should be an error
			},
			mockHash:        "",
			expectError:     true,
			expectSkipped:   false,
			expectFailed:    false,
			expectProcessed: false,
		},
		{
			name: "sendMessage error",
			data: func() *MockR3 {
				m := new(MockR3)
				m.On("GetType").Return(ProgramType)
				m.On("GetRapidID").Return("test-rapid-id")
				m.On("GetServiceID").Return("test-service-id")
				m.On("GetARID").Return("test-arid")
				return m
			}(),
			setupMocks: func(sqlMock sqlmock.Sqlmock, sqsMock *MockSQSClient, data *MockR3) {
				// DB mock
				sqlMock.ExpectQuery("SELECT").
					WithArgs("test-rapid-id", 1).
					WillReturnRows(sqlmock.NewRows([]string{"hash", "arid"}))

				// SQS mock - return error
				sqsMock.On("SendMessage", mock.Anything, mock.AnythingOfType("*sqs.SendMessageInput")).Return(
					&sqs.SendMessageOutput{MessageId: aws.String("")},
					errors.New("sqs error"),
				).Once()
			},
			mockHash:        "new-hash",
			expectError:     true,
			expectSkipped:   false,
			expectFailed:    true,
			expectProcessed: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Reset counters for each test
			processed = make(map[string]int)
			skipped = make(map[string]int)
			failed = make(map[string]int)

			// Setup mocks
			tt.setupMocks(sqlMock, mockSQSClient, tt.data)

			// Mock the hasher
			hasher = Hasher{
				Sum: func(b []byte) (string, error) {
					return tt.mockHash, nil
				},
			}

			// Call the function
			err := message(tt.data)

			// Verify results
			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			// Check counters
			if tt.expectSkipped {
				assert.Equal(t, 1, skipped[tt.data.GetType()])
			} else {
				assert.Equal(t, 0, skipped[tt.data.GetType()])
			}

			if tt.expectFailed {
				assert.Equal(t, 1, failed[tt.data.GetType()])
			} else {
				assert.Equal(t, 0, failed[tt.data.GetType()])
			}

			if tt.expectProcessed {
				assert.Equal(t, 1, processed[tt.data.GetType()])
			} else {
				assert.Equal(t, 0, processed[tt.data.GetType()])
			}

			// Verify all SQL expectations were met
			if err := sqlMock.ExpectationsWereMet(); err != nil {
				t.Errorf("Unfulfilled SQL expectations: %s", err)
			}

			// Verify all mock expectations
			tt.data.AssertExpectations(t)
			mockSQSClient.AssertExpectations(t)
		})
	}
}
