package r3

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
	"github.com/aws/aws-sdk-go-v2/service/sqs/types"
	"gorm.io/gorm"

	"stash.abc-dev.net.au/rmp/rmp-program-api/models"
)

type Hasher struct {
	Sum func([]byte) (string, error)
}

var hasher = Hasher{Sum: calculateHash}

type HashModel struct {
	Hash string `gorm:"column:hash"`
	ARID string `gorm:"column:arid"`
}

func message(data R3) error {
	input, err := getMessageInput(data)
	if err != nil {
		Logger().Error("error getting message input", "error", err.Error(), "entity", data.GetType(), "rapidID", data.GetRapidID())
		return err
	}
	// if input is nil, the entity has not changed, so we skip the message
	if input == nil {
		// add to skipped map
		skipped[data.GetType()]++
		return nil
	}

	err = sendMessage(input)
	if err != nil {
		failed[data.GetType()]++
		return err
	}
	// add to processed map
	processed[data.GetType()]++

	return nil
}

func getMessageInput(data R3) (*sqs.SendMessageInput, error) {
	logger := Logger().With("entity", data.GetType(), "rapidID", data.GetRapidID())
	// get the HashModel from the db
	hashModel, err := getHashFromDB(data)
	if err != nil {
		logger.Error("error getting hash from db", "error", err.Error())
		return nil, err
	}

	// if hashModel is not nil, check the arid
	if hashModel != nil && hashModel.ARID != data.GetARID() {
		logger.Debug("update ARID from db", "arid_from", data.GetARID(), "arid_to", hashModel.ARID)
		data.SetARID(hashModel.ARID)
	}

	b, err := json.Marshal(data)
	if err != nil {
		logger.Error("Error marshalling data", "error", err.Error())
		return nil, err
	}

	hash, err := hasher.Sum(b)
	if err != nil {
		logger.Error("Error hashing data", "error", err.Error())
		return nil, err
	}

	// if the hash has not changed, we skip the message
	if hashModel != nil && hashModel.Hash == hash {
		logger.Debug("skipping message as hash has not changed", "hash", hash)
		return nil, nil
	}

	message := &sqs.SendMessageInput{
		QueueUrl:    aws.String(QueueUrl),
		MessageBody: aws.String(string(b)),
		MessageAttributes: map[string]types.MessageAttributeValue{
			"HASH": {
				DataType:    aws.String("String"),
				StringValue: aws.String(hash),
			},
			"TYPE": {
				DataType:    aws.String("String"),
				StringValue: aws.String(data.GetType()),
			},
			"DATE": {
				DataType:    aws.String("String"),
				StringValue: aws.String(time.Now().Format(time.RFC3339)),
			},
			"SERVICE": {
				DataType:    aws.String("String"),
				StringValue: aws.String(data.GetServiceID()),
			},
			"ARID": {
				DataType:    aws.String("String"),
				StringValue: aws.String(data.GetARID()),
			},
			"RAPID_ID": {
				DataType:    aws.String("String"),
				StringValue: aws.String(data.GetRapidID()),
			},
		},
	}

	logger.Debug("created input message", "hash", hash, "message", message, "updated", hashModel != nil)

	return message, nil
}

func sendMessage(message *sqs.SendMessageInput) error {
	logger := Logger().With("message", message)
	logger.Debug("sending message")
	result, err := sqsClient.SendMessage(context.TODO(), message)
	if err != nil {
		logger.Error("error sending message", "error", err.Error(), "messageID", *result.MessageId)
		return err
	}
	logger.Debug("message sent", "messageID", *result.MessageId)
	return nil
}

func calculateHash(b []byte) (string, error) {
	hash := md5.New()
	_, err := hash.Write(b)
	if err != nil {
		return "", fmt.Errorf("error writing to hash: %v", err)
	}

	return hex.EncodeToString(hash.Sum(nil)), nil
}

func getHashFromDB(data R3) (*HashModel, error) {
	var query *gorm.DB

	switch data.GetType() {
	case ServiceType:
		query = db.Model(&models.Service{}).Where("service_id = ?", data.GetServiceID())
	case ProgramType:
		query = db.Model(&models.Program{}).Where("rapid = ?", data.GetRapidID())
	case SeriesType:
		query = db.Model(&models.Series{}).Where("rapid = ?", data.GetRapidID())
	case EpisodeType:
		query = db.Model(&models.Episode{}).Where("rapid = ?", data.GetRapidID())
	case PublicationEventType:
		query = db.Model(&models.PublicationEvent{}).Where("rapid = ?", data.GetRapidID())
	case VersionType:
		query = db.Model(&models.Version{}).Where("rapid = ?", data.GetRapidID())
	default:
		Logger().Error("unknown entity type", "entity", data.GetType(), "rapidID", data.GetRapidID())
		return nil, fmt.Errorf("unknown entity type")
	}

	var hashModel HashModel

	result := query.Limit(1).Find(&hashModel)

	if result.Error != nil {
		Logger().Error("error getting hash from db", "error", result.Error.Error(), "entity", data.GetType(), "rapidID", data.GetRapidID())
		return nil, result.Error
	}

	// return nil for new entity
	if result.RowsAffected == 0 {
		Logger().Debug("no matched db record", "entity", data.GetType(), "rapidID", data.GetRapidID())
		return nil, nil
	}

	return &hashModel, nil
}
