package r3

import (
	"stash.abc-dev.net.au/rmp/rmp-program-api/models"
	"stash.abc-dev.net.au/rmp/rmp-program-api/pkg/arid"
)

// Image represents an individual <image> element inside <images>
type Image struct {
	Title   string `xml:"title" json:"title,omitempty"`
	ARID    string `json:"arid,omitempty"`
	URL     string `xml:"url" json:"url,omitempty"`
	Role    string `xml:"role" json:"role,omitempty"`
	RapidID string `xml:"rapid_image_id" json:"parent_rapid_id,omitempty"`
}

func (i *Image) Process(parentRapidID string) {
	// If parent id is not set, set it to the program rapid id and role and url and use crc32b to generate arid
	if len(i.RapidID) == 0 {
		i.RapidID = parentRapidID + i.Role + i.URL
		i.ARID = arid.GenerateCrc32b(i.RapidID, models.ImageConfig)
	} else {
		// If rapid_image_id is set in xml, use default alder32 to generate arid
		i.ARID = i.GenerateARID()
	}
	i.URL = checkURL(i.URL)
}

func (i *Image) GenerateARID() string {
	return arid.Generate(i.RapidID, models.ImageConfig)
}
