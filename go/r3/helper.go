package r3

import (
	"log/slog"
	"net/url"
	"strings"
)

func checkURL(url string) string {
	if url == "" {
		return ""
	}

	// Convert http to https
	if strings.HasPrefix(url, "http://") {
		url = "https://" + url[7:]
	}

	// Replace mpegmedia.abc.net.au with abcmedia.akamaized.net
	if strings.Contains(url, "mpegmedia.abc.net.au") {
		slog.Info("Replacing mpegmedia.abc.net.au with abcmedia.akamaized.net", "url", url)
		url = strings.Replace(url, "mpegmedia.abc.net.au", "abcmedia.akamaized.net", 1)
	}

	return url
}

func validURL(str string) bool {
	u, err := url.ParseRequestURI(str)
	if err != nil {
		return false
	}
	// Check that the URL has a scheme and host
	if u.Scheme == "" || u.Host == "" {
		return false
	}
	return true
}

func trimSpace(str string) string {
	if strings.Contains(str, " ") {
		slog.Warn("Trimming whitespace from string", "string", str)
	}
	return strings.TrimSpace(str)
}

func processWebPage(webPage *WebPage, rapidID string) *WebPage {
	if webPage == nil {
		return nil
	}

	if webPage.URL == "" || strings.EqualFold(webPage.URL, "http://") || strings.EqualFold(webPage.URL, "https://") {
		return nil
	}

	webPage.Process(rapidID)

	return webPage
}
