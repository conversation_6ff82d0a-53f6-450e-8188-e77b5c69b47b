package r3

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestProgram_ProcessWebPage(t *testing.T) {
	tests := []struct {
		name     string
		program  Program
		expected *WebPage
	}{
		{
			name: "nil webpage",
			program: Program{
				PrimaryWebPage: nil,
			},
			expected: nil,
		},
		{
			name: "empty url",
			program: Program{
				PrimaryWebPage: &WebPage{
					URL: "",
				},
			},
			expected: nil,
		},
		{
			name: "http only url",
			program: Program{
				PrimaryWebPage: &WebPage{
					URL: "http://",
				},
			},
			expected: nil,
		},
		{
			name: "https only url",
			program: Program{
				PrimaryWebPage: &WebPage{
					URL: "https://",
				},
			},
			expected: nil,
		},
		{
			name: "valid url",
			program: Program{
				PrimaryWebPage: &WebPage{
					URL: "https://example.com",
				},
			},
			expected: &WebPage{
				URL: "https://example.com",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.program.Process(nil)
			if tt.expected != nil {
				tt.expected.Process(tt.program.RapidID)
			}
			assert.Equal(t, tt.expected, tt.program.PrimaryWebPage)
		})
	}
}
