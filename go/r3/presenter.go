package r3

import (
	"stash.abc-dev.net.au/rmp/rmp-program-api/models"
	"stash.abc-dev.net.au/rmp/rmp-program-api/pkg/arid"
)

// Presenter represents a single <presenter> element inside <presenters>
type Presenter struct {
	RapidID        string   `xml:"rapid_presenter_id" json:"rapid_id,omitempty"`
	ARID           string   `xml:"arid" json:"arid,omitempty"`
	FirstName      string   `xml:"firstName" json:"firstname"`
	LastName       string   `xml:"lastName" json:"lastname"`
	DisplayName    string   `xml:"displayName" json:"displayname"`
	PrimaryWebPage *WebPage `xml:"primary_webpage" json:"webpage"`
	Images         []*Image `xml:"images>image" json:"images"`
}

func (p *Presenter) Process() {
	if len(p.ARID) == 0 {
		p.ARID = p.GenerateARID()
	}

	p.PrimaryWebPage = processWebPage(p.PrimaryWebPage, p.RapidID)
	for _, image := range p.Images {
		image.Process(p.RapidID)
	}

	// expose empty array not null to match PHP version
	if len(p.Images) == 0 {
		p.Images = []*Image{}
	}
}

func (p *Presenter) GenerateARID() string {
	return arid.Generate(p.RapidID, models.PersonConfig)
}
