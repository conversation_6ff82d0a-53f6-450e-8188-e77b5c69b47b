package r3

import (
	"context"
	"encoding/xml"
	"log/slog"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
	"gorm.io/gorm"

	base "stash.abc-dev.net.au/rmp/rmp-program-api"
)

var QueueUrl string
var db *gorm.DB

// SQSClientAPI defines the interface for SQS client operations
type SQSClientAPI interface {
	SendMessage(ctx context.Context, params *sqs.SendMessageInput, optFns ...func(*sqs.Options)) (*sqs.SendMessageOutput, error)
}

var sqsClient SQSClientAPI

func ConnectToDB(config base.DBConfiguration) {
	if db != nil {
		return
	}
	var err error
	db, err = base.ConnectToDb(config)
	if err != nil {
		slog.Error("error connecting to db", "error", err.Error())
		panic(err)
	}
}

func ConnectToSQS(cfg aws.Config) {
	if sqsClient != nil {
		return
	}
	sqsClient = sqs.NewFromConfig(cfg)
}

type R3 interface {
	GetType() string
	GetARID() string
	GetRapidID() string
	GetServiceID() string
	SetARID(string)
}

type Schedule struct {
	XMLName  xml.Name  `xml:"schedule"`
	Service  *Service  `xml:"service"`
	Programs *Programs `xml:"programs"`
}
