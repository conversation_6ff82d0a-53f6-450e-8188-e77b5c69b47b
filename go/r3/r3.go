package r3

import (
	"context"
	"encoding/xml"
	"log/slog"
	"sync"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
	"gorm.io/gorm"

	base "stash.abc-dev.net.au/rmp/rmp-program-api"
)

var QueueUrl string
var db *gorm.DB

// Global logger with serviceID context
var (
	logger *slog.Logger
	loggerMutex sync.RWMutex
)

// SQSClientAPI defines the interface for SQS client operations
type SQSClientAPI interface {
	SendMessage(ctx context.Context, params *sqs.SendMessageInput, optFns ...func(*sqs.Options)) (*sqs.SendMessageOutput, error)
}

var sqsClient SQSClientAPI

// InitLogger initializes the global logger with serviceID context
func InitLogger(serviceID string) {
	loggerMutex.Lock()
	defer loggerMutex.Unlock()
	
	if serviceID != "" {
		logger = slog.With("serviceID", serviceID)
	} else {
		logger = slog.Default()
	}
}

// Logger returns the global logger, or default slog if not initialized
func Logger() *slog.Logger {
	loggerMutex.RLock()
	defer loggerMutex.RUnlock()
	
	if logger != nil {
		return logger
	}
	return slog.Default()
}

func ConnectToDB(config base.DBConfiguration) {
	if db != nil {
		return
	}
	var err error
	db, err = base.ConnectToDb(config)
	if err != nil {
		Logger().Error("error connecting to db", "error", err.Error())
		panic(err)
	}
}

func ConnectToSQS(cfg aws.Config) {
	if sqsClient != nil {
		return
	}
	sqsClient = sqs.NewFromConfig(cfg)
}

type R3 interface {
	GetType() string
	GetARID() string
	GetRapidID() string
	GetServiceID() string
	SetARID(string)
}

type Schedule struct {
	XMLName  xml.Name  `xml:"schedule"`
	Service  *Service  `xml:"service"`
	Programs *Programs `xml:"programs"`
}
