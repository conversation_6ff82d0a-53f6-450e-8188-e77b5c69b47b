package r3

import (
	"stash.abc-dev.net.au/rmp/rmp-program-api/models"
	"stash.abc-dev.net.au/rmp/rmp-program-api/pkg/arid"
)

const (
	ServiceType = "service"
)

// Service represents the <service> element inside <schedule>
type Service struct {
	EntityType string    `json:"type,omitempty"`
	ARID       string    `json:"arid,omitempty"`
	Title      string    `xml:"title" json:"title,omitempty"`
	Location   string    `xml:"location" json:"location,omitempty"`
	Timezone   string    `xml:"timezone" json:"timezone,omitempty"`
	RapidID    string    `xml:"rapid_service_id" json:"rapid_id,omitempty"`
	Outlets    []*Outlet `xml:"outlets>outlet" json:"outlets,omitempty"`
	ServiceID  string    `xml:"service_id" json:"-"`
}

func (s *Service) Process() {
	s.EntityType = ServiceType
	if len(s.ARID) == 0 {
		s.ARID = s.GenerateARID()
	}
	for _, outlet := range s.Outlets {
		outlet.Process()
	}
}

func (s *Service) GenerateARID() string {
	return arid.Generate(s.RapidID, models.ServiceConfig)
}

func (s *Service) GetType() string {
	return ServiceType
}

func (s *Service) GetARID() string {
	return s.ARID
}

func (s *Service) GetRapidID() string {
	return s.RapidID
}

func (s *Service) GetServiceID() string {
	return s.ServiceID
}

func (s *Service) SetARID(arid string) {
	s.ARID = arid
}
