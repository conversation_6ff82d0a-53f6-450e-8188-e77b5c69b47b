package r3

import (
	"stash.abc-dev.net.au/rmp/rmp-program-api/models"
	"stash.abc-dev.net.au/rmp/rmp-program-api/pkg/arid"
)

const (
	PublicationEventType = "publication_event"
	VersionType          = "version"
)

// PublicationEvent represents a single <publication_event> element inside <publication_events>
type PublicationEvent struct {
	EntityType     string   `json:"type,omitempty"` // it is called Type in other struct, called SQSType to avoid conflict
	EpisodeRapidID string   `json:"episode,omitempty"`
	SeriesRapidID  string   `json:"series,omitempty"`
	ProgramRapidID string   `json:"program,omitempty"`
	RapidID        string   `xml:"rapid_publication_event_id" json:"rapid_id,omitempty"`
	ARID           string   `json:"arid,omitempty"`
	Type           string   `xml:"type,attr" json:"publicationevent_type,omitempty"`
	StartTime      string   `xml:"start_time" json:"start_time,omitempty"`
	EndTime        string   `xml:"end_time" json:"end_time,omitempty"`
	Duration       int      `json:"duration,omitempty"`
	Outlets        []string `json:"outlets,omitempty"`
	ServiceID      string   `json:"-"`
	Episode        *Episode `json:"-"`
}

func (p *PublicationEvent) Process(episode *Episode) {
	p.EntityType = PublicationEventType
	if len(p.ARID) == 0 {
		p.ARID = p.GenerateARID()
	}
	if episode == nil {
		return
	}
	p.Episode = episode
	p.EpisodeRapidID = episode.RapidID
	if episode.Series == nil {
		return
	}
	p.SeriesRapidID = episode.Series.RapidID
	if episode.Series.Program == nil {
		return
	}
	p.ProgramRapidID = episode.Series.Program.RapidID
	if episode.Series.Program.Service == nil {
		return
	}
	p.ServiceID = episode.Series.Program.Service.ServiceID
	for _, outlet := range episode.Series.Program.Service.Outlets {
		p.Outlets = append(p.Outlets, outlet.Type)
	}
}

func (p *PublicationEvent) IsLive() bool {
	return p.Type == "Live" || p.Type == "Repeat"
}

func (p *PublicationEvent) GenerateARID() string {
	return arid.Generate(p.RapidID, models.PublicationEventConfig)
}

func (p *PublicationEvent) GetType() string {
	return p.EntityType
}

func (p *PublicationEvent) GetARID() string {
	return p.ARID
}

func (p *PublicationEvent) GetRapidID() string {
	return p.RapidID
}

func (p *PublicationEvent) GetServiceID() string {
	return p.ServiceID
}

func (p *PublicationEvent) SetARID(arid string) {
	p.ARID = arid
}

type Version struct {
	EntityType      string `json:"type,omitempty"`
	RapidID         string `json:"rapid_id,omitempty"`
	EpisodeRapidID  string `json:"episode,omitempty"`
	Title           string `json:"title,omitempty"`
	ARID            string `json:"arid,omitempty"`
	DurationSeconds int    `json:"durationSeconds,omitempty"`
	VersionType     int    `json:"version_type,omitempty"`
	ServiceID       string `json:"-"`
}

func (v *Version) GetType() string {
	return VersionType
}

func (v *Version) GetRapidID() string {
	return v.RapidID
}

func (v *Version) GetServiceID() string {
	return v.ServiceID
}

func (v *Version) GetARID() string {
	return v.ARID
}

func (v *Version) SetARID(arid string) {
	v.ARID = arid
}
