package r3

import (
	"stash.abc-dev.net.au/rmp/rmp-program-api/models"
	"stash.abc-dev.net.au/rmp/rmp-program-api/pkg/arid"
)

// WebPage represents the <primary_webpage> element inside <program>
type WebPage struct {
	ARID     string `json:"arid,omitempty"`
	Title    string `xml:"title" json:"title,omitempty"`
	URL      string `xml:"url" json:"url,omitempty"`
	ParentID string `json:"parent_rapid_id,omitempty"`
}

// Use processWebPage in helper.go to process the webpage
func (w *WebPage) Process(parentID string) {
	w.ParentID = parentID
	w.ARID = w.GenerateARID()
}

func (w *WebPage) GenerateARID() string {
	return arid.Generate(w.ParentID, models.WebpageConfig)
}
