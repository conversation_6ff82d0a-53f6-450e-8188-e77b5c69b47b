package r3

import (
	"stash.abc-dev.net.au/rmp/rmp-program-api/models"
	"stash.abc-dev.net.au/rmp/rmp-program-api/pkg/arid"
)

// AudioStream represents an <audio_stream> element with its properties
type AudioStream struct {
	Type          string `xml:"type,attr" json:"type,omitempty"`
	Specification string `xml:"specification,attr" json:"specification,omitempty"`
	URL           string `xml:"url" json:"url,omitempty"`
	ARID          string `xml:"arid" json:"arid,omitempty"`
	RapidID       string `xml:"rapid_audio_stream_id" json:"rapid_id,omitempty"`
}

func (a *AudioStream) Process() {
	if len(a.ARID) == 0 {
		a.ARID = a.GenerateARID()
	}
	// RMPAPI-3034 trim whitespace
	a.URL = trimSpace(a.URL)
}

func (a *AudioStream) GenerateARID() string {
	return arid.Generate(a.RapidID, models.AudioStreamConfig)
}
