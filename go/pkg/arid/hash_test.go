package arid

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestHashAdler32(t *testing.T) {
	hash := HashAdler32("1234567890")
	assert.Equal(t, "0b2c020e", hash)
}

func TestHashAdler32Webpage(t *testing.T) {
	hash := HashAdler32("dylanlewisradio.program.webpage15")
	assert.Equal(t, "e4880ce1", hash)
}

func TestHashCrc32b(t *testing.T) {
	hash := HashCrc32b("1234567890")
	assert.Equal(t, "261daee5", hash)
}

func TestHashCrc32(t *testing.T) {
	hash := HashCrc32("1234567890")
	assert.Equal(t, "b6536850", hash)
}
