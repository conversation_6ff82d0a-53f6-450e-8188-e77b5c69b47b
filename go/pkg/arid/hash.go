package arid

import (
	"fmt"
	"hash/adler32"
	"hash/crc32"

	"github.com/boguslaw-wojcik/crc32a"
)

type Hash struct {
	Sum func(string) string
}

// Hash implements the php hash function $string = hash('adler32', $string);
func HashAdler32(rapid_id string) string {
	hash := adler32.New()
	hash.Write([]byte(rapid_id))
	return fmt.Sprintf("%08x", hash.Sum32())
}

// crc32b
func HashCrc32b(rapid_id string) string {
	hash := crc32.NewIEEE()
	hash.Write([]byte(rapid_id))
	return fmt.Sprintf("%08x", hash.Sum32())
}

// crc32
func HashCrc32(rapid_id string) string {
	return crc32a.ChecksumHex([]byte(rapid_id))
}
