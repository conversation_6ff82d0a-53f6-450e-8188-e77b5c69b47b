package arid

import (
	"strconv"

	"stash.abc-dev.net.au/rmp/rmp-program-api/models"
)

// Generate generates an ARID for a given rapid ID, prefix, config path, and config ID
// The hash is generated using the Adler32 algorithm by default
// for other algorithms will be used in ingesotr to avoid collisions
func Generate(rapidID string, config models.AridConfig) string {
	hash := rapidID + config.Path + strconv.Itoa(config.ID)
	return config.Prefix + HashAdler32(hash)
}

func GenerateCrc32b(rapidID string, config models.AridConfig) string {
	hash := rapidID + config.Path + strconv.Itoa(config.ID)
	return config.Prefix + HashCrc32b(hash)
}
